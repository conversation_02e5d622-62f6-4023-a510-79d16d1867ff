"""
Recommendation Model Package
===========================

This package contains all recommendation system components:
- recommendation_model.py: Core ML model for generating recommendations
- recommendation_api.py: Flask API endpoints for recommendation services
- recommendation_analytics.py: Analytics and performance tracking
- recommendation_system_architecture.py: System architecture definitions

Author: Allora Development Team
Date: 2025-07-12
"""

from .recommendation_model import SeededDataRecommendationModel
from .recommendation_api import recommendation_api, init_recommendation_system
from .recommendation_analytics import RecommendationAnalytics
from .personalization_engine import PersonalizationEngine
from .user_behavior_tracker import BehaviorTracker
from .user_behavior_api import behavior_api, init_behavior_tracker
from .recommendation_system_architecture import (
    RecommendationAlgorithm,
    RecommendationType,
    UserInteractionType,
    UserInteraction,
    UserProfile,
    RecommendationRequest,
    RecommendationResult,
    RecommendationResponse,
    RecommendationSystemConfig,
    RecommendationSystemArchitecture
)

__all__ = [
    # Core model
    'SeededDataRecommendationModel',

    # API components
    'recommendation_api',
    'init_recommendation_system',

    # Analytics
    'RecommendationAnalytics',

    # Personalization
    'PersonalizationEngine',
    
    # Architecture components
    'RecommendationAlgorithm',
    'RecommendationType',
    'UserInteractionType',
    'UserInteraction',
    'UserProfile',
    'RecommendationRequest',
    'RecommendationResult',
    'RecommendationResponse',
    'RecommendationSystemConfig',
    'RecommendationSystemArchitecture'
]

__version__ = "1.0.0"
__author__ = "Allora Development Team"
