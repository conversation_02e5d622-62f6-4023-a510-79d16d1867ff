"""
Flask-SocketIO Manager for Real-time Features
============================================

Replaces FastAPI WebSocket implementation with Flask-SocketIO
for better integration with the existing Flask application.

Features:
1. Real-time inventory updates
2. Cart synchronization
3. Order status notifications
4. Price change alerts
5. Admin notifications
6. User authentication
7. Room-based broadcasting

Author: Allora Development Team
Date: 2025-07-13
"""

import logging
import json
import redis
from datetime import datetime
from typing import Dict, Set, Any, Optional
from flask import request, session
from flask_socketio import SocketIO, emit, join_room, leave_room, disconnect

logger = logging.getLogger(__name__)

class FlaskSocketIOManager:
    """Manages Flask-SocketIO connections and real-time events"""
    
    def __init__(self, app=None):
        self.socketio = None
        self.redis_client = None
        self.active_users = {}  # user_id -> session_id mapping
        self.guest_sessions = set()  # guest session IDs
        self.admin_sessions = set()  # admin session IDs
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize Flask-SocketIO with the Flask app"""
        try:
            # Initialize SocketIO
            self.socketio = SocketIO(
                app,
                cors_allowed_origins="*",
                logger=True,
                engineio_logger=True,
                async_mode='threading',
                ping_timeout=60,
                ping_interval=25
            )

            # Initialize Redis for pub/sub (with error handling)
            try:
                self.redis_client = redis.Redis(
                    host='localhost',
                    port=6379,
                    db=0,
                    decode_responses=True
                )
                # Test Redis connection
                self.redis_client.ping()
                logger.info("✅ Redis connected for Flask-SocketIO pub/sub")
            except Exception as redis_error:
                logger.warning(f"⚠️  Redis connection failed: {redis_error} - pub/sub disabled")
                self.redis_client = None

            # Register event handlers
            self._register_handlers()

            logger.info("Flask-SocketIO manager initialized successfully")

        except Exception as e:
            logger.error(f"Error initializing Flask-SocketIO manager: {e}")
            raise
    
    def _register_handlers(self):
        """Register SocketIO event handlers"""
        
        @self.socketio.on('connect')
        def handle_connect(auth=None):
            """Handle client connection"""
            try:
                # Get user info from session or auth
                user_id = None
                session_id = request.sid
                is_admin = False
                
                if auth and isinstance(auth, dict):
                    user_id = auth.get('user_id')
                    is_admin = auth.get('is_admin', False)
                
                # Join appropriate rooms
                if user_id:
                    join_room(f"user_{user_id}")
                    self.active_users[user_id] = session_id
                    
                    if is_admin:
                        join_room("admin")
                        self.admin_sessions.add(session_id)
                        
                    logger.info(f"User {user_id} connected via SocketIO")
                else:
                    join_room("guests")
                    self.guest_sessions.add(session_id)
                    logger.info(f"Guest {session_id} connected via SocketIO")
                
                # Send connection confirmation
                emit('connection_established', {
                    'status': 'connected',
                    'timestamp': datetime.now().isoformat(),
                    'user_id': user_id,
                    'session_id': session_id
                })
                
            except Exception as e:
                logger.error(f"Error handling connection: {e}")
                disconnect()
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """Handle client disconnection"""
            try:
                session_id = request.sid
                
                # Remove from tracking
                user_id = None
                for uid, sid in self.active_users.items():
                    if sid == session_id:
                        user_id = uid
                        break
                
                if user_id:
                    self.active_users.pop(user_id, None)
                    leave_room(f"user_{user_id}")
                    logger.info(f"User {user_id} disconnected from SocketIO")
                
                if session_id in self.admin_sessions:
                    self.admin_sessions.remove(session_id)
                    leave_room("admin")
                
                if session_id in self.guest_sessions:
                    self.guest_sessions.remove(session_id)
                    leave_room("guests")
                
            except Exception as e:
                logger.error(f"Error handling disconnection: {e}")
        
        @self.socketio.on('ping')
        def handle_ping():
            """Handle ping for keepalive"""
            emit('pong', {'timestamp': datetime.now().isoformat()})
        
        @self.socketio.on('subscribe')
        def handle_subscribe(data):
            """Handle subscription to specific events"""
            try:
                events = data.get('events', [])
                
                # Join event-specific rooms
                for event in events:
                    if event in ['inventory', 'prices', 'orders']:
                        join_room(f"events_{event}")
                
                emit('subscribed', {
                    'events': events,
                    'timestamp': datetime.now().isoformat()
                })
                
            except Exception as e:
                logger.error(f"Error handling subscription: {e}")
        
        @self.socketio.on('heartbeat')
        def handle_heartbeat():
            """Handle heartbeat for connection monitoring"""
            emit('heartbeat_ack', {'timestamp': datetime.now().isoformat()})
    
    # Real-time event broadcasting methods
    
    def broadcast_inventory_update(self, product_id: int, new_quantity: int, 
                                 old_quantity: int = None, variant_id: int = None):
        """Broadcast inventory update to all clients"""
        try:
            message = {
                'type': 'inventory_updated',
                'timestamp': datetime.now().isoformat(),
                'product_id': product_id,
                'variant_id': variant_id,
                'new_quantity': new_quantity,
                'old_quantity': old_quantity,
                'quantity_change': (new_quantity - old_quantity) if old_quantity else 0,
                'in_stock': new_quantity > 0
            }
            
            # Broadcast to all connected clients
            self.socketio.emit('inventory_update', message)
            
            # Also publish to Redis for other services
            self._publish_to_redis('inventory_updates', message)
            
            logger.info(f"Broadcasted inventory update for product {product_id}")
            
        except Exception as e:
            logger.error(f"Error broadcasting inventory update: {e}")
    
    def broadcast_price_update(self, product_id: int, new_price: float, old_price: float):
        """Broadcast price update to all clients"""
        try:
            message = {
                'type': 'price_updated',
                'timestamp': datetime.now().isoformat(),
                'product_id': product_id,
                'new_price': new_price,
                'old_price': old_price,
                'discount_percentage': round(((old_price - new_price) / old_price) * 100, 2) if old_price > 0 else 0
            }
            
            self.socketio.emit('price_update', message)
            self._publish_to_redis('price_updates', message)
            
            logger.info(f"Broadcasted price update for product {product_id}")
            
        except Exception as e:
            logger.error(f"Error broadcasting price update: {e}")
    
    def send_cart_update(self, user_id: str = None, session_id: str = None, cart_data: dict = None):
        """Send cart update to specific user or session"""
        try:
            message = {
                'type': 'cart_updated',
                'timestamp': datetime.now().isoformat(),
                'data': cart_data or {}
            }
            
            if user_id:
                self.socketio.emit('cart_update', message, room=f"user_{user_id}")
            elif session_id:
                self.socketio.emit('cart_update', message, room=session_id)
            
            logger.info(f"Sent cart update to user {user_id or session_id}")
            
        except Exception as e:
            logger.error(f"Error sending cart update: {e}")
    
    def send_order_status_update(self, user_id: str, order_id: int, status: str):
        """Send order status update to specific user"""
        try:
            message = {
                'type': 'order_status_updated',
                'timestamp': datetime.now().isoformat(),
                'order_id': order_id,
                'status': status
            }
            
            self.socketio.emit('order_update', message, room=f"user_{user_id}")
            
            logger.info(f"Sent order status update to user {user_id}")
            
        except Exception as e:
            logger.error(f"Error sending order status update: {e}")
    
    def send_notification(self, user_id: str, notification_data: dict):
        """Send notification to specific user"""
        try:
            message = {
                'type': 'notification',
                'timestamp': datetime.now().isoformat(),
                'data': notification_data
            }
            
            self.socketio.emit('notification', message, room=f"user_{user_id}")
            
            logger.info(f"Sent notification to user {user_id}")
            
        except Exception as e:
            logger.error(f"Error sending notification: {e}")
    
    def broadcast_to_admins(self, message: dict):
        """Broadcast message to admin users only"""
        try:
            if self.socketio:
                self.socketio.emit('admin_notification', message, room='admin')
                logger.info("Broadcasted message to admin users")
            else:
                logger.error("SocketIO not initialized - cannot broadcast to admins")
        except Exception as e:
            logger.error(f"Error broadcasting to admins: {e}")
    
    def broadcast_sync_status(self, channel_id: int, channel_name: str, 
                            status: str, product_id: int = None, error: str = None):
        """Broadcast sync status change to admins"""
        try:
            message = {
                'type': 'sync_status_changed',
                'timestamp': datetime.now().isoformat(),
                'channel_id': channel_id,
                'channel_name': channel_name,
                'status': status,
                'product_id': product_id,
                'error': error
            }
            
            self.broadcast_to_admins(message)
            self._publish_to_redis('sync_status_updates', message)
            
        except Exception as e:
            logger.error(f"Error broadcasting sync status: {e}")
    
    def broadcast_conflict_alert(self, conflict_id: int, product_id: int, 
                               product_name: str, conflict_type: str, priority: str):
        """Broadcast inventory conflict alert to admins"""
        try:
            message = {
                'type': 'conflict_detected',
                'timestamp': datetime.now().isoformat(),
                'conflict_id': conflict_id,
                'product_id': product_id,
                'product_name': product_name,
                'conflict_type': conflict_type,
                'priority': priority
            }
            
            self.broadcast_to_admins(message)
            self._publish_to_redis('conflict_alerts', message)
            
        except Exception as e:
            logger.error(f"Error broadcasting conflict alert: {e}")
    
    def _publish_to_redis(self, channel: str, message: dict):
        """Publish message to Redis pub/sub"""
        try:
            if self.redis_client:
                self.redis_client.publish(channel, json.dumps(message))
                logger.debug(f"Published message to Redis channel: {channel}")
        except Exception as e:
            logger.error(f"Error publishing to Redis channel {channel}: {e}")
            # Don't raise - Redis failure shouldn't break SocketIO
    
    def get_connection_stats(self):
        """Get connection statistics"""
        return {
            'total_connections': len(self.active_users) + len(self.guest_sessions),
            'authenticated_users': len(self.active_users),
            'guest_sessions': len(self.guest_sessions),
            'admin_sessions': len(self.admin_sessions),
            'active_users': list(self.active_users.keys())
        }

# Global manager instance
socketio_manager = FlaskSocketIOManager()

# Convenience functions for external use
def init_socketio(app):
    """Initialize SocketIO with Flask app"""
    socketio_manager.init_app(app)
    return socketio_manager.socketio

def get_socketio_instance():
    """Get the active SocketIO instance"""
    # First try global manager (most reliable)
    if socketio_manager and socketio_manager.socketio:
        return socketio_manager.socketio

    # Try to get from Flask app context
    try:
        from flask import current_app
        if hasattr(current_app, 'extensions') and 'socketio' in current_app.extensions:
            return current_app.extensions['socketio']
    except (RuntimeError, ImportError):
        # No app context or Flask not available
        pass

    # Last resort: try to import from app module (avoid circular imports)
    try:
        import sys
        if 'app' in sys.modules:
            app_module = sys.modules['app']
            if hasattr(app_module, 'socketio') and app_module.socketio:
                return app_module.socketio
    except (ImportError, AttributeError):
        pass

    logger.warning("SocketIO instance not available - real-time features disabled")
    return None

def broadcast_inventory_update(product_id: int, new_quantity: int, old_quantity: int = None):
    """Broadcast inventory update"""
    socketio_instance = get_socketio_instance()
    if socketio_instance:
        try:
            message = {
                'type': 'inventory_updated',
                'timestamp': datetime.now().isoformat(),
                'product_id': product_id,
                'variant_id': None,
                'old_quantity': old_quantity,
                'new_quantity': new_quantity,
                'quantity_change': (new_quantity - old_quantity) if old_quantity else 0,
                'in_stock': new_quantity > 0
            }
            socketio_instance.emit('inventory_update', message)
            logger.info(f"Broadcasted inventory update for product {product_id}")
        except Exception as e:
            logger.error(f"Error broadcasting inventory update: {e}")
    else:
        logger.error("SocketIO not available - cannot broadcast inventory update")

def broadcast_price_update(product_id: int, new_price: float, old_price: float):
    """Broadcast price update"""
    socketio_instance = get_socketio_instance()
    if socketio_instance:
        try:
            message = {
                'type': 'price_updated',
                'timestamp': datetime.now().isoformat(),
                'product_id': product_id,
                'new_price': new_price,
                'old_price': old_price,
                'discount_percentage': round(((old_price - new_price) / old_price) * 100, 2) if old_price > 0 else 0
            }
            socketio_instance.emit('price_update', message)
            logger.info(f"Broadcasted price update for product {product_id}")
        except Exception as e:
            logger.error(f"Error broadcasting price update: {e}")
    else:
        logger.error("SocketIO not available - cannot broadcast price update")

def send_cart_update(user_id: str = None, session_id: str = None, cart_data: dict = None):
    """Send cart update"""
    socketio_instance = get_socketio_instance()
    if socketio_instance:
        try:
            message = {
                'type': 'cart_updated',
                'timestamp': datetime.now().isoformat(),
                'data': cart_data or {}
            }
            if user_id:
                socketio_instance.emit('cart_update', message, room=f"user_{user_id}")
            elif session_id:
                socketio_instance.emit('cart_update', message, room=session_id)
            else:
                socketio_instance.emit('cart_update', message)
            logger.info(f"Sent cart update to user {user_id or session_id}")
        except Exception as e:
            logger.error(f"Error sending cart update: {e}")
    else:
        logger.error("SocketIO not available - cannot send cart update")

def send_order_status_update(user_id: str, order_id: int, status: str):
    """Send order status update"""
    socketio_instance = get_socketio_instance()
    if socketio_instance:
        try:
            message = {
                'type': 'order_status_updated',
                'timestamp': datetime.now().isoformat(),
                'order_id': order_id,
                'status': status
            }
            socketio_instance.emit('order_update', message, room=f"user_{user_id}")
            logger.info(f"Sent order status update to user {user_id}")
        except Exception as e:
            logger.error(f"Error sending order status update: {e}")
    else:
        logger.error("SocketIO not available - cannot send order status update")

def send_notification(user_id: str, notification_data: dict):
    """Send notification"""
    socketio_instance = get_socketio_instance()
    if socketio_instance:
        try:
            message = {
                'type': 'notification',
                'timestamp': datetime.now().isoformat(),
                'data': notification_data
            }
            socketio_instance.emit('notification', message, room=f"user_{user_id}")
            logger.info(f"Sent notification to user {user_id}")
        except Exception as e:
            logger.error(f"Error sending notification: {e}")
    else:
        logger.error("SocketIO not available - cannot send notification")

def broadcast_to_admins(message: dict):
    """Broadcast message to admin users only"""
    socketio_instance = get_socketio_instance()
    if socketio_instance:
        try:
            socketio_instance.emit('admin_notification', message, room='admin')
            logger.info("Broadcasted message to admin users")
        except Exception as e:
            logger.error(f"Error broadcasting to admins: {e}")
    else:
        logger.error("SocketIO not available - cannot broadcast to admins")
