"""
RMA Processing Engine
====================

Core business logic engine for processing Return Merchandise Authorization (RMA) requests.
Handles workflow automation, validation, approval processes, and integration with
existing systems.

Key Features:
1. RMA Request Validation
2. Automated Workflow Processing
3. Business Rules Engine
4. Approval Workflow Management
5. Integration with Order/Inventory Systems
6. Timeline and Audit Trail
7. Notification Management
8. Analytics and Reporting

Integration Points:
- Order/OrderItem models
- Inventory Management
- Payment/Refund System
- Shipping/Fulfillment System
- Notification Service
"""

import logging
import uuid
import json
import sys
import os
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any, Tuple
from enum import Enum
from dataclasses import dataclass

# Flask and database imports
from flask import current_app
from sqlalchemy import and_, or_, desc
from sqlalchemy.orm import joinedload

# Add backend path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# Import RMA architecture
from .rma_architecture import (
    RMAStatus, RMAType, ReturnReason, InspectionResult, RefundMethod,
    RMAConfiguration, RMAItem as RMAItemData, RMARequest as RMARequestData,
    RMAWorkflowRule, DEFAULT_RMA_RULES, RMA_CONFIG
)

logger = logging.getLogger(__name__)

class RMAProcessingError(Exception):
    """Custom exception for RMA processing errors"""
    pass

class RMAValidationError(Exception):
    """Custom exception for RMA validation errors"""
    pass

class RMAEngine:
    """Main RMA processing engine"""
    
    def __init__(self, db_session=None):
        self.db = db_session
        self.config = RMA_CONFIG
        self.workflow_rules = DEFAULT_RMA_RULES
        
    def create_rma_request(self, order_id: int, items: List[Dict], 
                          customer_info: Dict, rma_type: str = "return_refund") -> str:
        """Create a new RMA request"""
        try:
            from app import db, Order, OrderItem, RMARequest, RMAItem, RMATimeline
            
            # Validate order exists and is eligible
            order = db.session.query(Order).filter(Order.id == order_id).first()
            if not order:
                raise RMAValidationError(f"Order {order_id} not found")
            
            # Check if order is eligible for returns
            if not self._is_order_eligible_for_return(order):
                raise RMAValidationError("Order is not eligible for returns")
            
            # Generate RMA number
            rma_number = self._generate_rma_number()
            
            # Calculate deadline
            deadline = datetime.utcnow() + timedelta(days=self.config.return_window_days)
            
            # Create RMA request
            rma_request = RMARequest(
                rma_number=rma_number,
                order_id=order_id,
                user_id=order.user_id,
                rma_type=rma_type,
                status=RMAStatus.PENDING.value,
                customer_email=customer_info.get('email', order.user.email if order.user else ''),
                customer_phone=customer_info.get('phone'),
                customer_notes=customer_info.get('notes'),
                deadline=deadline,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            db.session.add(rma_request)
            db.session.flush()  # Get the ID
            
            # Create RMA items
            total_amount = 0
            for item_data in items:
                order_item = db.session.query(OrderItem).filter(
                    OrderItem.id == item_data['order_item_id']
                ).first()
                
                if not order_item:
                    raise RMAValidationError(f"Order item {item_data['order_item_id']} not found")
                
                rma_item = RMAItem(
                    rma_request_id=rma_request.id,
                    order_item_id=item_data['order_item_id'],
                    product_id=order_item.product_id,
                    quantity=item_data['quantity'],
                    unit_price=order_item.unit_price,
                    total_price=order_item.unit_price * item_data['quantity'],
                    return_reason=item_data['return_reason'],
                    condition_notes=item_data.get('condition_notes'),
                    created_at=datetime.utcnow()
                )
                
                db.session.add(rma_item)
                total_amount += rma_item.total_price
            
            # Update total amount
            rma_request.total_refund_amount = total_amount
            
            # Create initial timeline entry
            timeline_entry = RMATimeline(
                rma_request_id=rma_request.id,
                status=RMAStatus.PENDING.value,
                notes="RMA request created",
                created_by="system",
                created_at=datetime.utcnow()
            )
            db.session.add(timeline_entry)
            
            # Process workflow rules
            self._process_workflow_rules(rma_request)
            
            db.session.commit()
            
            logger.info(f"RMA request {rma_number} created successfully")
            return rma_number
            
        except Exception as e:
            if self.db:
                self.db.rollback()
            logger.error(f"Error creating RMA request: {e}")
            raise RMAProcessingError(f"Failed to create RMA request: {e}")
    
    def _generate_rma_number(self) -> str:
        """Generate unique RMA number"""
        timestamp = datetime.utcnow().strftime("%Y%m%d")
        unique_id = str(uuid.uuid4())[:8].upper()
        return f"RMA-{timestamp}-{unique_id}"
    
    def _is_order_eligible_for_return(self, order) -> bool:
        """Check if order is eligible for returns"""
        # Check order status
        if order.status not in ['delivered', 'shipped']:
            return False
        
        # Check time limits
        if order.delivered_at:
            days_since_delivery = (datetime.utcnow() - order.delivered_at).days
            if days_since_delivery > self.config.return_window_days:
                return False
        elif order.shipped_at:
            days_since_shipped = (datetime.utcnow() - order.shipped_at).days
            if days_since_shipped > (self.config.return_window_days + 7):
                return False
        else:
            return False
        
        return True

    def _process_workflow_rules(self, rma_request):
        """Process workflow rules for RMA request"""
        try:
            # Sort rules by priority
            sorted_rules = sorted(self.workflow_rules, key=lambda x: x.priority)

            for rule in sorted_rules:
                if not rule.active:
                    continue

                # Create context for rule evaluation
                context = {
                    'total_amount': rma_request.total_refund_amount,
                    'item_count': len(rma_request.rma_items) if hasattr(rma_request, 'rma_items') else 0,
                    'rma_type': rma_request.rma_type,
                    'return_reasons': []  # Will be populated from items
                }

                # Evaluate rule
                if rule.evaluate_context(context):
                    self._execute_rule_action(rma_request, rule)
                    break  # Execute only the first matching rule

        except Exception as e:
            logger.error(f"Error processing workflow rules: {e}")

    def _execute_rule_action(self, rma_request, rule):
        """Execute action based on workflow rule"""
        try:
            from app import db, RMATimeline

            if rule.action == 'auto_approve':
                rma_request.status = RMAStatus.APPROVED.value
                rma_request.approved_by = 'system'
                rma_request.approved_at = datetime.utcnow()
                rma_request.requires_approval = False

                # Add timeline event
                self._add_timeline_event(
                    rma_request.id,
                    'auto_approved',
                    f'RMA request auto-approved by rule: {rule.name}',
                    'system'
                )

            elif rule.action == 'require_approval':
                rma_request.requires_approval = True

            elif rule.action == 'auto_reject':
                rma_request.status = RMAStatus.REJECTED.value
                rma_request.rejection_reason = f'Auto-rejected by rule: {rule.name}'

                # Add timeline event
                self._add_timeline_event(
                    rma_request.id,
                    'auto_rejected',
                    f'RMA request auto-rejected by rule: {rule.name}',
                    'system'
                )

        except Exception as e:
            logger.error(f"Error executing rule action: {e}")

    def _add_timeline_event(self, rma_request_id: int, event_type: str,
                           description: str, created_by: str, created_by_id: int = None,
                           old_status: str = None, new_status: str = None, notes: str = None):
        """Add timeline event for RMA request"""
        try:
            from app import db, RMATimeline

            timeline_entry = RMATimeline(
                rma_request_id=rma_request_id,
                event_type=event_type,
                description=description,
                old_status=old_status,
                new_status=new_status,
                notes=notes,
                created_by=created_by,
                created_by_id=created_by_id,
                created_at=datetime.utcnow()
            )

            db.session.add(timeline_entry)

        except Exception as e:
            logger.error(f"Error adding timeline event: {e}")

    def _generate_return_label(self, rma_request):
        """Generate return shipping label"""
        try:
            # This would integrate with shipping carriers
            # For now, we'll create a placeholder
            rma_request.return_label_url = f"https://returns.allora.com/label/{rma_request.rma_number}"
            rma_request.return_carrier = "UPS"
            rma_request.return_tracking_number = f"1Z{rma_request.rma_number[-10:]}"

        except Exception as e:
            logger.error(f"Error generating return label: {e}")

    def _send_rma_notification(self, rma_request, event_type: str):
        """Send notification for RMA event"""
        try:
            # This would integrate with notification service
            logger.info(f"Notification sent for RMA {rma_request.rma_number}: {event_type}")

        except Exception as e:
            logger.error(f"Error sending RMA notification: {e}")

    def _initiate_refund_process(self, rma_request):
        """Initiate refund process for approved RMA"""
        try:
            from app import db

            # Update status to refund processing
            rma_request.status = RMAStatus.REFUND_PROCESSING.value
            rma_request.refund_method = RefundMethod.ORIGINAL_PAYMENT.value
            rma_request.updated_at = datetime.utcnow()

            # Add timeline event
            self._add_timeline_event(
                rma_request.id,
                'refund_initiated',
                'Refund process initiated',
                'system'
            )

            # This would integrate with payment gateway for actual refund
            # For now, we'll mark as completed
            self._complete_refund(rma_request)

        except Exception as e:
            logger.error(f"Error initiating refund process: {e}")

    def _complete_refund(self, rma_request):
        """Complete refund process"""
        try:
            from app import db

            # Update status to refund completed
            rma_request.status = RMAStatus.REFUND_COMPLETED.value
            rma_request.refund_reference = f"REF-{rma_request.rma_number}"
            rma_request.refunded_at = datetime.utcnow()
            rma_request.updated_at = datetime.utcnow()

            # Add timeline event
            self._add_timeline_event(
                rma_request.id,
                'refund_completed',
                f'Refund completed: {rma_request.refund_reference}',
                'system'
            )

            # Update inventory if needed
            self._update_inventory_for_return(rma_request)

        except Exception as e:
            logger.error(f"Error completing refund: {e}")

    def _update_inventory_for_return(self, rma_request):
        """Update inventory for returned items"""
        try:
            from app import db, Product

            for rma_item in rma_request.rma_items:
                if rma_item.inspection_result == 'passed':
                    product = db.session.query(Product).get(rma_item.product_id)
                    if product:
                        product.stock_quantity += rma_item.quantity
                        product.updated_at = datetime.utcnow()

            db.session.commit()

        except Exception as e:
            logger.error(f"Error updating inventory: {e}")
            db.session.rollback()
