#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from waitress import serve
from app import app

# Configuration
DEBUG_ROUTES = os.getenv('DEBUG_ROUTES', 'false').lower() == 'true'
HOST = os.getenv('HOST', '127.0.0.1')  # Use 0.0.0.0 for public access
PORT = int(os.getenv('PORT', 5000))
THREADS = int(os.getenv('THREADS', 6))

if __name__ == '__main__':
    print("🚀 Starting Allora Flask app with Waitress WSGI server...")
    print("📊 Routes registered:", len(list(app.url_map.iter_rules())))
    print(f"🌐 Server will be available at: http://{HOST}:{PORT}")
    print(f"🔧 Using Waitress with {THREADS} threads instead of Flask development server")
    print("=" * 50)

    # Initialize the app context and database
    with app.app_context():
        from app import db, initialize_payment_gateways, initialize_admin_user, initialize_oauth_providers
        try:
            db.create_all()
            print("✅ Database tables created/verified")

            # Initialize components with error handling
            try:
                initialize_payment_gateways()
                print("✅ Payment gateways initialized")
            except Exception as e:
                print(f"⚠️  Payment gateways initialization failed: {e}")

            try:
                initialize_admin_user()
                print("✅ Admin user initialized")
            except Exception as e:
                print(f"⚠️  Admin user initialization failed: {e}")

            try:
                initialize_oauth_providers()
                print("✅ OAuth providers initialized")
            except Exception as e:
                print(f"⚠️  OAuth providers initialization failed: {e}")

            print("✅ All initialization completed")
        except Exception as e:
            print(f"⚠️  Warning during initialization: {e}")

    # Optional: Print routes for debugging (set DEBUG_ROUTES=true environment variable)
    if DEBUG_ROUTES:
        print("\n=== REGISTERED ROUTES ===")
        for rule in app.url_map.iter_rules():
            print(f"{rule.rule} -> {rule.endpoint} [{', '.join(rule.methods)}]")
        print(f"Total routes: {len(list(app.url_map.iter_rules()))}")

    print("🎯 Starting server...")

    # Check if SocketIO is available and use it for WebSocket support
    try:
        from app import socketio
        if socketio:
            print("🔌 Starting with Flask-SocketIO support for WebSocket connections...")
            print("⚠️  Note: Using SocketIO.run() instead of Waitress for WebSocket compatibility")
            socketio.run(app, host=HOST, port=PORT, debug=False, allow_unsafe_werkzeug=True)
        else:
            print("⚠️  SocketIO not available, using Waitress (WebSockets will not work)...")
            serve(app, host=HOST, port=PORT, threads=THREADS)
    except ImportError:
        print("⚠️  SocketIO not available, using Waitress (WebSockets will not work)...")
        serve(app, host=HOST, port=PORT, threads=THREADS)
