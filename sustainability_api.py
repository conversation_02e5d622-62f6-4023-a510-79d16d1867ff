"""
Sustainability Impact Dashboard API
==================================

Admin-configurable APIs for managing sustainability metrics, environmental impact tracking,
and green initiatives dashboard content.

Features:
- Real-time CO2 savings tracking
- Tree planting metrics
- Sustainability goals management
- Green heroes (top sustainable products)
- Environmental impact calculations
- Admin-configurable sustainability content

Author: Allora Development Team
Date: 2025-07-11
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from datetime import datetime, timedelta
import json
import logging
from typing import Dict, List, Optional, Any
from decimal import Decimal
import math

# Set up logging
logger = logging.getLogger(__name__)

# Create blueprint with versioning
sustainability_bp = Blueprint('sustainability', __name__, url_prefix='/api/v1/sustainability')

# Database models will be imported lazily to avoid circular imports
def get_models():
    """Get database models using lazy import"""
    try:
        from app import db, User, Product, Order, OrderItem, AdminUser
        return db, User, Product, Order, OrderItem, AdminUser
    except ImportError:
        return None, None, None, None, None, None

def admin_required(f):
    """Decorator to require admin authentication"""
    @jwt_required()
    def decorated_function(*args, **kwargs):
        try:
            current_user_id = get_jwt_identity()
            db, User, Product, Order, OrderItem, AdminUser = get_models()
            
            if not db:
                return jsonify({'error': 'Database not available'}), 500
            
            # Check if user is admin
            admin_user = AdminUser.query.filter_by(user_id=current_user_id).first()
            if not admin_user:
                return jsonify({'error': 'Admin access required'}), 403
                
            return f(*args, **kwargs)
        except Exception as e:
            logger.error(f"Admin authentication error: {e}")
            return jsonify({'error': 'Authentication failed'}), 401
    
    decorated_function.__name__ = f.__name__
    return decorated_function

# ============================================================================
# SUSTAINABILITY METRICS CALCULATION
# ============================================================================

def calculate_co2_savings(orders_data):
    """Calculate CO2 savings from sustainable purchases"""
    total_co2_saved = 0
    
    for order in orders_data:
        for item in order.get('items', []):
            # Estimate CO2 savings based on sustainability score
            sustainability_score = item.get('sustainability_score', 0)
            quantity = item.get('quantity', 1)
            
            # CO2 savings formula: higher sustainability score = more CO2 saved
            # Assume 1 sustainability point = 0.1 kg CO2 saved per item
            co2_per_item = (sustainability_score / 100) * 0.5  # kg CO2
            total_co2_saved += co2_per_item * quantity
    
    return round(total_co2_saved, 2)

def calculate_trees_planted_equivalent(co2_saved):
    """Calculate equivalent trees planted based on CO2 saved"""
    # Average tree absorbs ~22 kg CO2 per year
    trees_equivalent = co2_saved / 22
    return round(trees_equivalent, 1)

def calculate_plastic_saved(orders_data):
    """Calculate plastic waste saved from eco-friendly purchases"""
    total_plastic_saved = 0
    
    for order in orders_data:
        for item in order.get('items', []):
            if item.get('recyclable', False) or item.get('organic', False):
                # Estimate plastic savings based on product type
                quantity = item.get('quantity', 1)
                # Assume eco-friendly products save ~50g plastic per item
                total_plastic_saved += 0.05 * quantity  # kg
    
    return round(total_plastic_saved, 2)

# ============================================================================
# PUBLIC SUSTAINABILITY METRICS
# ============================================================================

@sustainability_bp.route('/metrics', methods=['GET'])
def get_sustainability_metrics():
    """Get current sustainability impact metrics"""
    try:
        db, User, Product, Order, OrderItem, AdminUser = get_models()

        if not db:
            return jsonify({'error': 'Database not available'}), 500

        # Get time range (default: last 30 days)
        days = request.args.get('days', 30, type=int)
        start_date = datetime.utcnow() - timedelta(days=days)

        # Get orders with sustainability data
        orders = Order.query.filter(Order.created_at >= start_date).all()
        
        # Prepare orders data for calculations
        orders_data = []
        for order in orders:
            order_items = []
            for item in order.items:
                product = item.product
                order_items.append({
                    'product_id': product.id,
                    'quantity': item.quantity,
                    'sustainability_score': getattr(product, 'sustainability_score', 0) or 0,
                    'carbon_footprint': getattr(product, 'carbon_footprint', 0),
                    'recyclable': getattr(product, 'recyclable', False),
                    'organic': getattr(product, 'organic', False),
                    'carbon_neutral': getattr(product, 'carbon_neutral', False)
                })
            orders_data.append({'items': order_items})

        # Calculate metrics
        co2_saved = calculate_co2_savings(orders_data)
        trees_equivalent = calculate_trees_planted_equivalent(co2_saved)
        plastic_saved = calculate_plastic_saved(orders_data)

        # Get total sustainable products sold
        sustainable_products_sold = sum(
            sum(item['quantity'] for item in order['items'] if item['sustainability_score'] > 70)
            for order in orders_data
        )

        # Get top sustainable categories
        category_impact = {}
        for order in orders_data:
            for item in order['items']:
                if item['sustainability_score'] > 70:
                    product = Product.query.get(item['product_id'])
                    if product and product.category:
                        category = product.category
                        if category not in category_impact:
                            category_impact[category] = 0
                        category_impact[category] += item['quantity']

        top_categories = sorted(category_impact.items(), key=lambda x: x[1], reverse=True)[:5]

        metrics = {
            'period_days': days,
            'co2_saved_kg': co2_saved,
            'trees_planted_equivalent': trees_equivalent,
            'plastic_saved_kg': plastic_saved,
            'sustainable_products_sold': sustainable_products_sold,
            'total_orders': len(orders),
            'sustainability_rate': round((sustainable_products_sold / max(sum(sum(item['quantity'] for item in order['items']) for order in orders_data), 1)) * 100, 1),
            'top_sustainable_categories': [{'category': cat, 'impact_score': score} for cat, score in top_categories],
            'last_updated': datetime.utcnow().isoformat()
        }

        return jsonify({
            'success': True,
            'data': metrics
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting sustainability metrics: {e}")
        return jsonify({'error': 'Failed to get sustainability metrics'}), 500

@sustainability_bp.route('/green-heroes', methods=['GET'])
def get_green_heroes():
    """Get top sustainable products (Green Heroes)"""
    try:
        db, User, Product, Order, OrderItem, AdminUser = get_models()

        if not db:
            return jsonify({'error': 'Database not available'}), 500

        # Get parameters
        limit = request.args.get('limit', 10, type=int)
        days = request.args.get('days', 30, type=int)

        # Get products with high sustainability scores and recent sales
        start_date = datetime.utcnow() - timedelta(days=days)

        # Check if sustainability_score column exists, if not use fallback
        try:
            # Query for green heroes
            green_heroes = db.session.query(
                Product,
                db.func.sum(OrderItem.quantity).label('total_sold'),
                db.func.avg(OrderItem.quantity * getattr(Product, 'sustainability_score', 70)).label('impact_score')
            ).join(
                OrderItem, Product.id == OrderItem.product_id
            ).join(
                Order, OrderItem.order_id == Order.id
            ).filter(
                Order.created_at >= start_date
            ).group_by(
                Product.id
            ).order_by(
                db.desc('impact_score')
            ).limit(limit).all()
        except Exception as query_error:
            logger.warning(f"Green heroes query failed, using fallback: {query_error}")
            # Fallback: get recent products
            green_heroes = db.session.query(
                Product,
                db.func.sum(OrderItem.quantity).label('total_sold'),
                db.func.avg(OrderItem.quantity * 70).label('impact_score')
            ).join(
                OrderItem, Product.id == OrderItem.product_id
            ).join(
                Order, OrderItem.order_id == Order.id
            ).filter(
                Order.created_at >= start_date
            ).group_by(
                Product.id
            ).order_by(
                db.desc('total_sold')
            ).limit(limit).all()
        
        heroes_data = []
        for product, total_sold, impact_score in green_heroes:
            sustainability_score = getattr(product, 'sustainability_score', 70)
            heroes_data.append({
                'id': product.id,
                'name': product.name,
                'sustainability_score': sustainability_score,
                'carbon_footprint': getattr(product, 'carbon_footprint', 0),
                'total_sold': int(total_sold) if total_sold else 0,
                'impact_score': float(impact_score) if impact_score else 0,
                'price': float(product.price) if product.price else 0,
                'image_url': getattr(product, 'image_url', ''),
                'category': getattr(product, 'category', 'General'),
                'organic': getattr(product, 'organic', False),
                'carbon_neutral': getattr(product, 'carbon_neutral', False),
                'recyclable': getattr(product, 'recyclable', False),
                'eco_badges': []
            })

            # Add eco badges
            if getattr(product, 'organic', False):
                heroes_data[-1]['eco_badges'].append('Organic')
            if getattr(product, 'carbon_neutral', False):
                heroes_data[-1]['eco_badges'].append('Carbon Neutral')
            if getattr(product, 'recyclable', False):
                heroes_data[-1]['eco_badges'].append('Recyclable')
            if sustainability_score >= 90:
                heroes_data[-1]['eco_badges'].append('Eco Champion')

        return jsonify({
            'success': True,
            'data': heroes_data,
            'period_days': days
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting green heroes: {e}")
        return jsonify({'error': 'Failed to get green heroes'}), 500

@sustainability_bp.route('/goals', methods=['GET'])
def get_sustainability_goals():
    """Get sustainability goals and progress"""
    try:
        # For now, return sample goals (in production, store in database)
        current_metrics = get_sustainability_metrics()
        if current_metrics[1] != 200:
            return current_metrics

        metrics_data = current_metrics[0].get_json()['data']
        
        goals = [
            {
                'id': 1,
                'title': 'CO2 Reduction Goal',
                'description': 'Save 1000 kg of CO2 this month',
                'target': 1000,
                'current': metrics_data['co2_saved_kg'],
                'unit': 'kg CO2',
                'progress': min(round((metrics_data['co2_saved_kg'] / 1000) * 100, 1), 100),
                'icon': '🌍',
                'color': 'green'
            },
            {
                'id': 2,
                'title': 'Tree Planting Equivalent',
                'description': 'Plant equivalent of 50 trees this month',
                'target': 50,
                'current': metrics_data['trees_planted_equivalent'],
                'unit': 'trees',
                'progress': min(round((metrics_data['trees_planted_equivalent'] / 50) * 100, 1), 100),
                'icon': '🌳',
                'color': 'emerald'
            },
            {
                'id': 3,
                'title': 'Sustainable Products',
                'description': 'Sell 500 sustainable products this month',
                'target': 500,
                'current': metrics_data['sustainable_products_sold'],
                'unit': 'products',
                'progress': min(round((metrics_data['sustainable_products_sold'] / 500) * 100, 1), 100),
                'icon': '♻️',
                'color': 'blue'
            },
            {
                'id': 4,
                'title': 'Plastic Waste Reduction',
                'description': 'Save 100 kg of plastic waste this month',
                'target': 100,
                'current': metrics_data['plastic_saved_kg'],
                'unit': 'kg plastic',
                'progress': min(round((metrics_data['plastic_saved_kg'] / 100) * 100, 1), 100),
                'icon': '🗑️',
                'color': 'purple'
            }
        ]

        return jsonify({
            'success': True,
            'data': goals
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting sustainability goals: {e}")
        return jsonify({'error': 'Failed to get sustainability goals'}), 500

# ============================================================================
# ADMIN SUSTAINABILITY MANAGEMENT
# ============================================================================

@sustainability_bp.route('/admin/goals', methods=['GET'])
@admin_required
def get_admin_sustainability_goals():
    """Get sustainability goals for admin management"""
    try:
        # In production, fetch from database
        goals = [
            {
                'id': 1,
                'title': 'Monthly CO2 Reduction',
                'target': 1000,
                'unit': 'kg CO2',
                'active': True,
                'created_at': datetime.utcnow().isoformat()
            },
            {
                'id': 2,
                'title': 'Tree Planting Equivalent',
                'target': 50,
                'unit': 'trees',
                'active': True,
                'created_at': datetime.utcnow().isoformat()
            }
        ]
        
        return jsonify({
            'success': True,
            'data': goals
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting admin sustainability goals: {e}")
        return jsonify({'error': 'Failed to get admin sustainability goals'}), 500

@sustainability_bp.route('/admin/goals', methods=['POST'])
@admin_required
def create_sustainability_goal():
    """Create a new sustainability goal"""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['title', 'target', 'unit']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400
        
        # Create new goal (in production, save to database)
        new_goal = {
            'id': len(data) + 1,  # In production, use auto-increment
            'title': data['title'],
            'description': data.get('description', ''),
            'target': data['target'],
            'unit': data['unit'],
            'active': data.get('active', True),
            'created_at': datetime.utcnow().isoformat()
        }
        
        return jsonify({
            'success': True,
            'data': new_goal,
            'message': 'Sustainability goal created successfully'
        }), 201
        
    except Exception as e:
        logger.error(f"Error creating sustainability goal: {e}")
        return jsonify({'error': 'Failed to create sustainability goal'}), 500

@sustainability_bp.route('/admin/impact-settings', methods=['GET'])
@admin_required
def get_impact_settings():
    """Get sustainability impact calculation settings"""
    try:
        settings = {
            'co2_calculation': {
                'enabled': True,
                'formula': 'sustainability_score * 0.005 kg per item',
                'base_rate': 0.005
            },
            'tree_calculation': {
                'enabled': True,
                'co2_per_tree': 22,  # kg CO2 per tree per year
                'formula': 'co2_saved / 22'
            },
            'plastic_calculation': {
                'enabled': True,
                'base_savings': 0.05,  # kg per eco-friendly item
                'formula': '0.05 kg per recyclable/organic item'
            },
            'display_settings': {
                'show_real_time': True,
                'update_frequency': 'hourly',
                'show_goals': True,
                'show_green_heroes': True
            }
        }
        
        return jsonify({
            'success': True,
            'data': settings
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting impact settings: {e}")
        return jsonify({'error': 'Failed to get impact settings'}), 500

# Export blueprint
def register_sustainability_api(app):
    """Register sustainability API with Flask app"""
    app.register_blueprint(sustainability_bp)
    logger.info("Sustainability API registered successfully")
