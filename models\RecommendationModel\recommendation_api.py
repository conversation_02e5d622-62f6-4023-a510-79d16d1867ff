"""
Recommendation API System
========================

Comprehensive recommendation API with multiple endpoints for different
recommendation types, A/B testing support, and performance monitoring.

Endpoints:
- Personalized recommendations
- Trending products
- Similar products
- Cross-sell recommendations
- Upsell recommendations
- Category-based recommendations
- Recently viewed recommendations

Author: Allora Development Team
Date: 2025-07-06
"""

from flask import Blueprint, request, jsonify, session
from datetime import datetime, timedelta
import json
import time
import uuid
from typing import Dict, List, Optional, Any, Tuple
import logging
import sys
import os

# Add backend path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

# Set up logging
logger = logging.getLogger(__name__)

# Import recommendation components
from .personalization_engine import PersonalizationEngine
# Note: Using new v5.0 model structure - model saved directly in models/Recommendation Model/
from .recommendation_analytics import RecommendationAnalytics
from .recommendation_system_architecture import (
    RecommendationRequest, RecommendationResult, RecommendationType,
    UserInteractionType
)

# Database models will be imported lazily to avoid circular imports
def get_models():
    """Get database models using lazy import"""
    try:
        from app import db, User, Product, UserInteractionLog, UserBehaviorProfile
        return db, User, Product, UserInteractionLog, UserBehaviorProfile
    except ImportError:
        return None, None, None, None, None

# Global variables for models (will be set during initialization)
db = None
User = None
Product = None
UserInteractionLog = None
UserBehaviorProfile = None

def init_models():
    """Initialize models to avoid circular imports"""
    global db, User, Product, UserInteractionLog, UserBehaviorProfile
    try:
        from app import db as _db, User as _User, Product as _Product, UserInteractionLog as _UserInteractionLog, UserBehaviorProfile as _UserBehaviorProfile
        db, User, Product, UserInteractionLog, UserBehaviorProfile = _db, _User, _Product, _UserInteractionLog, _UserBehaviorProfile
        return True
    except ImportError:
        return False

# Create Blueprint
recommendation_api = Blueprint('recommendation_api', __name__)

# Initialize components (will be set up in main app)
personalization_engine = None
analytics_system = None

def init_recommendation_system(db_session, redis_client):
    """Initialize the recommendation system components"""
    global personalization_engine, analytics_system

    # Initialize models first to avoid circular imports
    if not init_models():
        logger.warning("Failed to initialize database models for recommendation system")
        return False

    personalization_engine = PersonalizationEngine(redis_client, db_session)
    # Note: Using organized model structure - models are loaded from pkl files in app.py
    analytics_system = RecommendationAnalytics(db_session)
    return True

@recommendation_api.route('/api/recommendations/personalized', methods=['GET'])
def get_personalized_recommendations():
    """Get personalized recommendations for a user"""
    try:
        # Get request parameters
        user_id = request.args.get('user_id')
        limit = request.args.get('limit', 10, type=int)
        category = request.args.get('category')
        
        if not user_id:
            return jsonify({'error': 'User ID is required'}), 400
        
        # Validate limit
        if limit > 50:
            limit = 50
        
        # Create context data
        context_data = {
            'request_time': datetime.utcnow().isoformat(),
            'user_agent': request.headers.get('User-Agent', ''),
            'ip_address': request.remote_addr
        }
        
        # Create recommendation request
        rec_request = RecommendationRequest(
            user_id=str(user_id),
            recommendation_type=RecommendationType.PERSONALIZED,
            limit=limit,
            context=context_data,
            filters={'category': category} if category else {}
        )
        
        # Get recommendations
        if personalization_engine:
            result = personalization_engine.get_personalized_recommendations(rec_request)
        else:
            result = _get_fallback_recommendations(user_id, limit)
        
        # Format response
        db, User, Product, UserInteractionLog, UserBehaviorProfile = get_models()
        if not Product:
            return jsonify({'error': 'Database models not available'}), 500
        
        recommendations = []
        for item_id, score in result.recommendations:
            product = Product.query.get(item_id)
            if product:
                recommendations.append({
                    'product_id': int(item_id),
                    'score': float(score),
                    'product': {
                        'id': product.id,
                        'name': product.name,
                        'price': float(product.price) if product.price else 0.0,
                        'image_url': product.image,
                        'category': product.category,
                        'brand': getattr(product, 'brand', ''),
                        'average_rating': float(product.average_rating) if product.average_rating else 0.0
                    },
                    'reason': result.explanation or f"Recommended based on your preferences"
                })
        
        return jsonify({
            'success': True,
            'recommendations': recommendations,
            'total_count': len(recommendations),
            'algorithm_used': result.algorithm,
            'user_id': user_id,
            'timestamp': result.timestamp.isoformat(),
            'confidence_score': result.confidence_score
        })
        
    except Exception as e:
        logger.error(f"Error getting personalized recommendations: {e}")
        return jsonify({'error': 'Failed to get personalized recommendations'}), 500

@recommendation_api.route('/api/recommendations/similar/<int:product_id>', methods=['GET'])
def get_similar_products(product_id):
    """Get products similar to a specific product"""
    try:
        limit = request.args.get('limit', 10, type=int)
        
        # Validate limit
        if limit > 50:
            limit = 50
        
        # Get models
        db, User, Product, UserInteractionLog, UserBehaviorProfile = get_models()
        if not Product:
            return jsonify({'error': 'Database models not available'}), 500
        
        # Check if product exists
        base_product = Product.query.get(product_id)
        if not base_product:
            return jsonify({'error': 'Product not found'}), 404
        
        # Get similar products using fallback method (organized model handles this in app.py)
        # Note: Advanced recommendations are handled by organized model structure
        similar_items = _get_similar_products_fallback(base_product, limit)
        
        # Format response
        recommendations = []
        for similar_product_id, similarity_score in similar_items:
            product = Product.query.get(similar_product_id)
            if product:
                recommendations.append({
                    'product_id': similar_product_id,
                    'similarity_score': similarity_score,
                    'product': {
                        'id': product.id,
                        'name': product.name,
                        'price': product.price,
                        'image_url': product.image,
                        'category': product.category,
                        'brand': getattr(product, 'brand', ''),
                        'average_rating': product.average_rating
                    },
                    'reason': f"Similar to {base_product.name}"
                })
        
        return jsonify({
            'success': True,
            'base_product_id': product_id,
            'recommendations': recommendations,
            'total_count': len(recommendations),
            'algorithm_used': 'content_based_similarity',
            'timestamp': datetime.utcnow().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error getting similar products: {e}")
        return jsonify({'error': 'Failed to get similar products'}), 500

@recommendation_api.route('/api/recommendations/trending', methods=['GET'])
def get_trending_recommendations():
    """Get trending product recommendations"""
    try:
        limit = request.args.get('limit', 10, type=int)
        category = request.args.get('category')
        time_window = request.args.get('time_window', 7, type=int)  # days
        
        # Validate parameters
        if limit > 50:
            limit = 50
        if time_window > 30:
            time_window = 30
        
        # Get models
        db, User, Product, UserInteractionLog, UserBehaviorProfile = get_models()
        if not Product:
            return jsonify({'error': 'Database models not available'}), 500
        
        # Calculate trending products based on recent interactions
        cutoff_date = datetime.utcnow() - timedelta(days=time_window)
        
        # Get trending products query
        trending_query = db.session.query(
            Product.id,
            db.func.count(UserInteractionLog.id).label('interaction_count'),
            db.func.count(db.distinct(UserInteractionLog.user_id)).label('unique_users'),
            db.func.avg(UserInteractionLog.value).label('avg_engagement')
        ).join(
            UserInteractionLog, Product.id == UserInteractionLog.product_id
        ).filter(
            UserInteractionLog.created_at >= cutoff_date,
            UserInteractionLog.interaction_type.in_(['view', 'click', 'purchase', 'add_to_cart'])
        )
        
        if category:
            trending_query = trending_query.filter(Product.category == category)
        
        trending_products = trending_query.group_by(Product.id)\
            .order_by(db.desc('interaction_count'), db.desc('unique_users'))\
            .limit(limit).all()
        
        # Format response
        recommendations = []
        for product_id, interaction_count, unique_users, avg_engagement in trending_products:
            product = Product.query.get(product_id)
            if product:
                # Calculate trending score
                trending_score = (interaction_count * 0.6) + (unique_users * 0.4)
                
                recommendations.append({
                    'product_id': product_id,
                    'trending_score': round(trending_score, 2),
                    'product': {
                        'id': product.id,
                        'name': product.name,
                        'price': float(product.price) if product.price else 0.0,
                        'image_url': product.image,
                        'category': product.category,
                        'brand': getattr(product, 'brand', ''),
                        'average_rating': float(product.average_rating) if product.average_rating else 0.0
                    },
                    'stats': {
                        'interaction_count': int(interaction_count),
                        'unique_users': int(unique_users),
                        'avg_engagement': float(avg_engagement) if avg_engagement else 0.0
                    },
                    'reason': f"Trending with {unique_users} users in last {time_window} days"
                })
        
        return jsonify({
            'success': True,
            'recommendations': recommendations,
            'total_count': len(recommendations),
            'algorithm_used': 'trending_analysis',
            'time_window_days': time_window,
            'category_filter': category,
            'timestamp': datetime.utcnow().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error getting trending recommendations: {e}")
        return jsonify({'error': 'Failed to get trending recommendations'}), 500

# Helper functions

def _get_fallback_recommendations(user_id: int, limit: int) -> RecommendationResult:
    """Fallback recommendations when personalization engine is not available"""
    # Get models
    db, User, Product, UserInteractionLog, UserBehaviorProfile = get_models()
    if not Product:
        return RecommendationResult(
            user_id=str(user_id),
            recommendations=[],
            algorithm='fallback_error',
            context={},
            timestamp=datetime.utcnow(),
            confidence_score=0.0
        )

    # Get popular products
    popular_products = Product.query.order_by(
        Product.average_rating.desc()
    ).limit(limit).all()

    recommendations = [(str(p.id), p.average_rating or 3.0) for p in popular_products]

    return RecommendationResult(
        user_id=str(user_id),
        recommendations=recommendations,
        algorithm='fallback_popular',
        context={},
        timestamp=datetime.utcnow(),
        confidence_score=0.5
    )

def _get_similar_products_fallback(base_product, limit: int) -> List[Tuple[int, float]]:
    """Fallback method for getting similar products"""
    # Get models
    db, User, Product, UserInteractionLog, UserBehaviorProfile = get_models()
    if not Product:
        return []

    # Simple similarity based on category and price range
    price_range = (base_product.price * 0.7, base_product.price * 1.3) if base_product.price else (0, float('inf'))

    similar_products = Product.query.filter(
        Product.category == base_product.category,
        Product.id != base_product.id,
        Product.price.between(price_range[0], price_range[1]) if base_product.price else True
    ).order_by(Product.average_rating.desc()).limit(limit).all()

    # Assign similarity scores based on rating and category match
    results = []
    for product in similar_products:
        similarity_score = 0.8  # Base similarity for same category
        if product.average_rating and base_product.average_rating:
            rating_diff = abs(product.average_rating - base_product.average_rating)
            similarity_score += (1.0 - rating_diff / 5.0) * 0.2  # Rating similarity

        results.append((product.id, min(similarity_score, 1.0)))

    return results

# Export main components
__all__ = ['recommendation_api', 'init_recommendation_system']
