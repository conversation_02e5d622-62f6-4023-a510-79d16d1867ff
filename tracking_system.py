"""
Real-time Tracking System
========================

Comprehensive tracking system that monitors shipment status across all carriers
and provides real-time updates to customers and administrators.

Features:
1. Real-time shipment tracking across multiple carriers
2. Automated status synchronization
3. Exception detection and handling
4. Performance monitoring and analytics
5. Integration with fulfillment engine
6. Webhook processing for carrier updates
"""

import logging
import json
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor, as_completed

from sqlalchemy import and_, or_, func, desc
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

logger = logging.getLogger(__name__)

# ============================================================================
# ENUMS AND DATA STRUCTURES
# ============================================================================

class TrackingStatus(Enum):
    """Standardized tracking statuses across all carriers"""
    LABEL_CREATED = "label_created"
    PICKUP_SCHEDULED = "pickup_scheduled"
    PICKED_UP = "picked_up"
    IN_TRANSIT = "in_transit"
    OUT_FOR_DELIVERY = "out_for_delivery"
    DELIVERED = "delivered"
    DELIVERY_ATTEMPTED = "delivery_attempted"
    RETURNED_TO_SENDER = "returned_to_sender"
    LOST = "lost"
    DAMAGED = "damaged"
    EXCEPTION = "exception"
    CANCELLED = "cancelled"

class TrackingEventType(Enum):
    """Types of tracking events"""
    STATUS_UPDATE = "status_update"
    LOCATION_UPDATE = "location_update"
    DELIVERY_ATTEMPT = "delivery_attempt"
    EXCEPTION_OCCURRED = "exception_occurred"
    CARRIER_UPDATE = "carrier_update"
    SYSTEM_UPDATE = "system_update"
    ESTIMATED_DELIVERY_UPDATE = "estimated_delivery_update"

class NotificationChannel(Enum):
    """Notification delivery channels"""
    EMAIL = "email"
    SMS = "sms"
    PUSH = "push"
    WEBHOOK = "webhook"
    WEBSOCKET = "websocket"

@dataclass
class TrackingEvent:
    """Individual tracking event"""
    tracking_number: str
    status: TrackingStatus
    event_type: TrackingEventType
    timestamp: datetime
    location: Optional[str] = None
    description: Optional[str] = None
    carrier_code: Optional[str] = None
    carrier_status: Optional[str] = None
    estimated_delivery: Optional[datetime] = None
    exception_code: Optional[str] = None
    exception_description: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class TrackingInfo:
    """Complete tracking information for a shipment"""
    tracking_number: str
    order_id: int
    carrier_code: str
    current_status: TrackingStatus
    estimated_delivery: Optional[datetime]
    actual_delivery: Optional[datetime]
    origin_location: Optional[str]
    destination_location: Optional[str]
    events: List[TrackingEvent]
    last_updated: datetime
    exception_count: int = 0
    is_delayed: bool = False
    delay_reason: Optional[str] = None

# ============================================================================
# CARRIER STATUS MAPPING
# ============================================================================

class CarrierStatusMapper:
    """Maps carrier-specific statuses to standardized tracking statuses"""
    
    # Shiprocket status mappings
    SHIPROCKET_MAPPING = {
        'Order Confirmed': TrackingStatus.LABEL_CREATED,
        'Pickup Scheduled': TrackingStatus.PICKUP_SCHEDULED,
        'Picked Up': TrackingStatus.PICKED_UP,
        'In Transit': TrackingStatus.IN_TRANSIT,
        'Out for Delivery': TrackingStatus.OUT_FOR_DELIVERY,
        'Delivered': TrackingStatus.DELIVERED,
        'Delivery Attempted': TrackingStatus.DELIVERY_ATTEMPTED,
        'RTO': TrackingStatus.RETURNED_TO_SENDER,
        'Exception': TrackingStatus.EXCEPTION,
        'Lost': TrackingStatus.LOST,
        'Damaged': TrackingStatus.DAMAGED
    }
    
    # Delhivery status mappings
    DELHIVERY_MAPPING = {
        'Manifested': TrackingStatus.LABEL_CREATED,
        'Pickup Scheduled': TrackingStatus.PICKUP_SCHEDULED,
        'Picked': TrackingStatus.PICKED_UP,
        'In transit': TrackingStatus.IN_TRANSIT,
        'Reached at destination': TrackingStatus.IN_TRANSIT,
        'Out for Delivery': TrackingStatus.OUT_FOR_DELIVERY,
        'Delivered': TrackingStatus.DELIVERED,
        'Undelivered': TrackingStatus.DELIVERY_ATTEMPTED,
        'RTO': TrackingStatus.RETURNED_TO_SENDER,
        'Lost': TrackingStatus.LOST,
        'Damaged': TrackingStatus.DAMAGED
    }
    
    # FedEx status mappings
    FEDEX_MAPPING = {
        'Label Created': TrackingStatus.LABEL_CREATED,
        'Pickup Scheduled': TrackingStatus.PICKUP_SCHEDULED,
        'Picked up': TrackingStatus.PICKED_UP,
        'In transit': TrackingStatus.IN_TRANSIT,
        'On FedEx vehicle for delivery': TrackingStatus.OUT_FOR_DELIVERY,
        'Delivered': TrackingStatus.DELIVERED,
        'Delivery attempted': TrackingStatus.DELIVERY_ATTEMPTED,
        'Return to sender': TrackingStatus.RETURNED_TO_SENDER,
        'Exception': TrackingStatus.EXCEPTION,
        'Lost': TrackingStatus.LOST,
        'Package damaged': TrackingStatus.DAMAGED
    }
    
    @classmethod
    def map_status(cls, carrier_code: str, carrier_status: str) -> TrackingStatus:
        """Map carrier-specific status to standardized status"""
        carrier_code = carrier_code.lower()
        
        if carrier_code == 'shiprocket':
            return cls.SHIPROCKET_MAPPING.get(carrier_status, TrackingStatus.EXCEPTION)
        elif carrier_code == 'delhivery':
            return cls.DELHIVERY_MAPPING.get(carrier_status, TrackingStatus.EXCEPTION)
        elif carrier_code == 'fedex':
            return cls.FEDEX_MAPPING.get(carrier_status, TrackingStatus.EXCEPTION)
        else:
            logger.warning(f"Unknown carrier code: {carrier_code}")
            return TrackingStatus.EXCEPTION

# ============================================================================
# REAL-TIME TRACKING SYSTEM
# ============================================================================

class RealTimeTrackingSystem:
    """Core tracking system that monitors shipments across all carriers"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.tracking_cache: Dict[str, TrackingInfo] = {}
        self.active_trackings: Dict[str, bool] = {}
        self.tracking_lock = threading.RLock()
        self.status_mapper = CarrierStatusMapper()
        
        # Tracking configuration
        self.polling_interval = 300  # 5 minutes
        self.max_concurrent_tracks = 50
        self.exception_threshold = 3
        self.delay_threshold_hours = 24
        
        # Start background tracking thread
        self._start_tracking_thread()
    
    def _start_tracking_thread(self):
        """Start background thread for continuous tracking"""
        def tracking_worker():
            while True:
                try:
                    self._poll_all_active_shipments()
                    time.sleep(self.polling_interval)
                except Exception as e:
                    logger.error(f"Error in tracking worker: {e}")
                    time.sleep(60)  # Wait 1 minute before retrying
        
        tracking_thread = threading.Thread(target=tracking_worker, daemon=True)
        tracking_thread.start()
        logger.info("Real-time tracking system started")
    
    def start_tracking(self, tracking_number: str, order_id: int, carrier_code: str) -> bool:
        """Start tracking a shipment"""
        try:
            with self.tracking_lock:
                # Check if already tracking
                if tracking_number in self.active_trackings:
                    logger.info(f"Already tracking shipment {tracking_number}")
                    return True
                
                # Initialize tracking info
                tracking_info = TrackingInfo(
                    tracking_number=tracking_number,
                    order_id=order_id,
                    carrier_code=carrier_code,
                    current_status=TrackingStatus.LABEL_CREATED,
                    estimated_delivery=None,
                    actual_delivery=None,
                    origin_location=None,
                    destination_location=None,
                    events=[],
                    last_updated=datetime.now()
                )
                
                self.tracking_cache[tracking_number] = tracking_info
                self.active_trackings[tracking_number] = True
                
                # Perform initial tracking
                self._track_single_shipment(tracking_number)
                
                logger.info(f"Started tracking shipment {tracking_number} for order {order_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error starting tracking for {tracking_number}: {e}")
            return False
    
    def stop_tracking(self, tracking_number: str) -> bool:
        """Stop tracking a shipment"""
        try:
            with self.tracking_lock:
                if tracking_number in self.active_trackings:
                    del self.active_trackings[tracking_number]
                    logger.info(f"Stopped tracking shipment {tracking_number}")
                    return True
                return False
                
        except Exception as e:
            logger.error(f"Error stopping tracking for {tracking_number}: {e}")
            return False
    
    def get_tracking_info(self, tracking_number: str) -> Optional[TrackingInfo]:
        """Get current tracking information for a shipment"""
        with self.tracking_lock:
            return self.tracking_cache.get(tracking_number)
    
    def _poll_all_active_shipments(self):
        """Poll all active shipments for updates"""
        try:
            active_shipments = list(self.active_trackings.keys())
            
            if not active_shipments:
                return
            
            logger.info(f"Polling {len(active_shipments)} active shipments")
            
            # Use thread pool for concurrent tracking
            with ThreadPoolExecutor(max_workers=self.max_concurrent_tracks) as executor:
                future_to_tracking = {
                    executor.submit(self._track_single_shipment, tracking_number): tracking_number
                    for tracking_number in active_shipments
                }
                
                for future in as_completed(future_to_tracking):
                    tracking_number = future_to_tracking[future]
                    try:
                        future.result()
                    except Exception as e:
                        logger.error(f"Error tracking {tracking_number}: {e}")
                        
        except Exception as e:
            logger.error(f"Error polling shipments: {e}")
    
    def _track_single_shipment(self, tracking_number: str):
        """Track a single shipment and update its status"""
        try:
            tracking_info = self.tracking_cache.get(tracking_number)
            if not tracking_info:
                return
            
            # Get carrier tracking data
            carrier_data = self._get_carrier_tracking_data(
                tracking_number, 
                tracking_info.carrier_code
            )
            
            if not carrier_data:
                return
            
            # Process tracking events
            new_events = self._process_carrier_data(tracking_info, carrier_data)
            
            if new_events:
                # Update tracking info
                tracking_info.events.extend(new_events)
                tracking_info.last_updated = datetime.now()
                
                # Update current status to latest event
                if new_events:
                    latest_event = max(new_events, key=lambda e: e.timestamp)
                    tracking_info.current_status = latest_event.status
                    
                    if latest_event.estimated_delivery:
                        tracking_info.estimated_delivery = latest_event.estimated_delivery
                    
                    if latest_event.status == TrackingStatus.DELIVERED:
                        tracking_info.actual_delivery = latest_event.timestamp
                        # Stop tracking delivered shipments
                        self.stop_tracking(tracking_number)
                
                # Check for delays and exceptions
                self._check_for_delays_and_exceptions(tracking_info)
                
                # Save to database
                self._save_tracking_events(tracking_info, new_events)
                
                # Trigger notifications
                self._trigger_notifications(tracking_info, new_events)
                
                logger.info(f"Updated tracking for {tracking_number}: {len(new_events)} new events")
                
        except Exception as e:
            logger.error(f"Error tracking shipment {tracking_number}: {e}")
    
    def _get_carrier_tracking_data(self, tracking_number: str, carrier_code: str) -> Optional[Dict[str, Any]]:
        """Get tracking data from carrier API"""
        try:
            from carrier_integration import CarrierFactory, ShippingCarrier
            
            # Get carrier instance
            carrier_enum = ShippingCarrier(carrier_code)
            carrier_factory = CarrierFactory()
            carrier_api = carrier_factory.get_carrier(carrier_enum)
            
            # Track shipment
            tracking_data = carrier_api.track_shipment(tracking_number)
            
            return tracking_data if tracking_data.get('success') else None
            
        except Exception as e:
            logger.error(f"Error getting carrier data for {tracking_number}: {e}")
            return None
    
    def _process_carrier_data(self, tracking_info: TrackingInfo, carrier_data: Dict[str, Any]) -> List[TrackingEvent]:
        """Process carrier tracking data and create tracking events"""
        new_events = []
        
        try:
            # Extract events from carrier data
            carrier_events = carrier_data.get('events', [])
            existing_timestamps = {event.timestamp for event in tracking_info.events}
            
            for carrier_event in carrier_events:
                # Parse event timestamp
                event_timestamp = self._parse_timestamp(carrier_event.get('timestamp'))
                
                # Skip if we already have this event
                if event_timestamp in existing_timestamps:
                    continue
                
                # Map carrier status to standardized status
                carrier_status = carrier_event.get('status', '')
                standardized_status = self.status_mapper.map_status(
                    tracking_info.carrier_code, 
                    carrier_status
                )
                
                # Create tracking event
                event = TrackingEvent(
                    tracking_number=tracking_info.tracking_number,
                    status=standardized_status,
                    event_type=self._determine_event_type(standardized_status),
                    timestamp=event_timestamp,
                    location=carrier_event.get('location'),
                    description=carrier_event.get('description'),
                    carrier_code=tracking_info.carrier_code,
                    carrier_status=carrier_status,
                    estimated_delivery=self._parse_timestamp(carrier_event.get('estimated_delivery')),
                    exception_code=carrier_event.get('exception_code'),
                    exception_description=carrier_event.get('exception_description'),
                    metadata=carrier_event.get('metadata')
                )
                
                new_events.append(event)
            
            return new_events
            
        except Exception as e:
            logger.error(f"Error processing carrier data: {e}")
            return []
    
    def _parse_timestamp(self, timestamp_str: Optional[str]) -> Optional[datetime]:
        """Parse timestamp string to datetime object"""
        if not timestamp_str:
            return None
        
        try:
            # Try different timestamp formats
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%dT%H:%M:%S',
                '%Y-%m-%dT%H:%M:%SZ',
                '%Y-%m-%d %H:%M:%S.%f',
                '%d/%m/%Y %H:%M:%S'
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(timestamp_str, fmt)
                except ValueError:
                    continue
            
            logger.warning(f"Could not parse timestamp: {timestamp_str}")
            return None
            
        except Exception as e:
            logger.error(f"Error parsing timestamp {timestamp_str}: {e}")
            return None
    
    def _determine_event_type(self, status: TrackingStatus) -> TrackingEventType:
        """Determine event type based on status"""
        if status in [TrackingStatus.EXCEPTION, TrackingStatus.LOST, TrackingStatus.DAMAGED]:
            return TrackingEventType.EXCEPTION_OCCURRED
        elif status == TrackingStatus.DELIVERY_ATTEMPTED:
            return TrackingEventType.DELIVERY_ATTEMPT
        else:
            return TrackingEventType.STATUS_UPDATE
    
    def _check_for_delays_and_exceptions(self, tracking_info: TrackingInfo):
        """Check for delays and exceptions in shipment"""
        try:
            # Check for delays
            if tracking_info.estimated_delivery:
                now = datetime.now()
                if (now > tracking_info.estimated_delivery and 
                    tracking_info.current_status not in [TrackingStatus.DELIVERED, TrackingStatus.RETURNED_TO_SENDER]):
                    
                    delay_hours = (now - tracking_info.estimated_delivery).total_seconds() / 3600
                    if delay_hours > self.delay_threshold_hours:
                        tracking_info.is_delayed = True
                        tracking_info.delay_reason = f"Delayed by {int(delay_hours)} hours"
            
            # Count exceptions
            exception_events = [
                event for event in tracking_info.events 
                if event.event_type == TrackingEventType.EXCEPTION_OCCURRED
            ]
            tracking_info.exception_count = len(exception_events)
            
        except Exception as e:
            logger.error(f"Error checking delays and exceptions: {e}")
    
    def _save_tracking_events(self, tracking_info: TrackingInfo, new_events: List[TrackingEvent]):
        """Save tracking events to database"""
        try:
            from app import TrackingEvent as TrackingEventModel
            
            for event in new_events:
                db_event = TrackingEventModel(
                    tracking_number=event.tracking_number,
                    order_id=tracking_info.order_id,
                    status=event.status.value,
                    event_type=event.event_type.value,
                    timestamp=event.timestamp,
                    location=event.location,
                    description=event.description,
                    carrier_code=event.carrier_code,
                    carrier_status=event.carrier_status,
                    estimated_delivery=event.estimated_delivery,
                    exception_code=event.exception_code,
                    exception_description=event.exception_description,
                    metadata=json.dumps(event.metadata) if event.metadata else None
                )
                
                self.db.add(db_event)
            
            self.db.commit()
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error saving tracking events: {e}")
    
    def _trigger_notifications(self, tracking_info: TrackingInfo, new_events: List[TrackingEvent]):
        """Trigger notifications for tracking updates"""
        try:
            from notification_service import get_notification_service
            
            notification_service = get_notification_service()
            
            for event in new_events:
                # Determine if this event should trigger a notification
                if self._should_notify(event):
                    notification_service.send_tracking_notification(
                        tracking_info.order_id,
                        tracking_info.tracking_number,
                        event
                    )
                    
        except Exception as e:
            logger.error(f"Error triggering notifications: {e}")
    
    def _should_notify(self, event: TrackingEvent) -> bool:
        """Determine if an event should trigger a notification"""
        # Notify for major status changes
        notify_statuses = [
            TrackingStatus.PICKED_UP,
            TrackingStatus.OUT_FOR_DELIVERY,
            TrackingStatus.DELIVERED,
            TrackingStatus.DELIVERY_ATTEMPTED,
            TrackingStatus.EXCEPTION,
            TrackingStatus.RETURNED_TO_SENDER
        ]
        
        return event.status in notify_statuses
    
    def get_tracking_summary(self) -> Dict[str, Any]:
        """Get summary of all tracking activities"""
        try:
            with self.tracking_lock:
                total_active = len(self.active_trackings)
                
                status_counts = {}
                delayed_count = 0
                exception_count = 0
                
                for tracking_info in self.tracking_cache.values():
                    status = tracking_info.current_status.value
                    status_counts[status] = status_counts.get(status, 0) + 1
                    
                    if tracking_info.is_delayed:
                        delayed_count += 1
                    
                    if tracking_info.exception_count > 0:
                        exception_count += 1
                
                return {
                    'total_active_trackings': total_active,
                    'status_distribution': status_counts,
                    'delayed_shipments': delayed_count,
                    'shipments_with_exceptions': exception_count,
                    'last_updated': datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"Error getting tracking summary: {e}")
            return {'error': str(e)}

# ============================================================================
# FACTORY AND INTEGRATION
# ============================================================================

def create_tracking_system(db_session: Session) -> RealTimeTrackingSystem:
    """Create a new tracking system instance"""
    return RealTimeTrackingSystem(db_session)

# Global tracking system instance
_global_tracking_system = None
_tracking_system_lock = threading.Lock()

def get_tracking_system(db_session: Session) -> RealTimeTrackingSystem:
    """Get global tracking system instance"""
    global _global_tracking_system
    
    if _global_tracking_system is None:
        with _tracking_system_lock:
            if _global_tracking_system is None:
                _global_tracking_system = RealTimeTrackingSystem(db_session)
    
    return _global_tracking_system
