"""
RMA Utility Functions
====================

Utility functions for RMA processing including:
1. Validation helpers
2. Calculation utilities
3. Notification helpers
4. Report generation
5. Data export/import
6. Integration helpers

These utilities support the main RMA engine and provide
reusable functionality across the RMA system.
"""

import logging
import json
import csv
import io
import sys
import os
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Optional, Any, Tu<PERSON>
from decimal import Decimal, ROUND_HALF_UP

# Add backend path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

logger = logging.getLogger(__name__)

class RMAValidator:
    """RMA validation utilities"""
    
    @staticmethod
    def validate_return_eligibility(order, config) -> Tuple[bool, str]:
        """Validate if order is eligible for returns"""
        
        # Check order status
        if order.status not in ['delivered', 'shipped']:
            return False, "Order must be delivered or shipped to be eligible for returns"
        
        # Check time limits
        if order.delivered_at:
            days_since_delivery = (datetime.utcnow() - order.delivered_at).days
            if days_since_delivery > config.return_window_days:
                return False, f"Return window of {config.return_window_days} days has expired"
        elif order.shipped_at:
            days_since_shipped = (datetime.utcnow() - order.shipped_at).days
            if days_since_shipped > (config.return_window_days + 7):
                return False, "Return window has expired"
        else:
            return False, "Order must be shipped to be eligible for returns"
        
        # Check if order has existing RMA requests
        from app import RMARequest
        existing_rma = RMARequest.query.filter_by(order_id=order.id).first()
        if existing_rma and existing_rma.status not in ['rejected', 'cancelled']:
            return False, "Order already has an active RMA request"
        
        return True, "Order is eligible for returns"
    
    @staticmethod
    def validate_return_items(order_items: List, return_items: List[Dict]) -> Tuple[bool, str]:
        """Validate return items against order items"""
        
        order_items_dict = {item.id: item for item in order_items}
        
        for return_item in return_items:
            order_item_id = return_item.get('order_item_id')
            return_quantity = return_item.get('quantity', 0)
            
            # Check if order item exists
            if order_item_id not in order_items_dict:
                return False, f"Order item {order_item_id} not found"
            
            order_item = order_items_dict[order_item_id]
            
            # Check quantity
            if return_quantity <= 0:
                return False, "Return quantity must be greater than 0"
            
            if return_quantity > order_item.quantity:
                return False, f"Return quantity ({return_quantity}) exceeds ordered quantity ({order_item.quantity})"
            
            # Check return reason
            return_reason = return_item.get('return_reason')
            if not return_reason:
                return False, "Return reason is required"
            
            valid_reasons = [
                'defective', 'wrong_item', 'not_as_described', 'size_issue',
                'color_issue', 'damaged_shipping', 'changed_mind', 'better_price_found',
                'duplicate_order', 'quality_issue', 'missing_parts', 'expired_product', 'other'
            ]
            
            if return_reason not in valid_reasons:
                return False, f"Invalid return reason: {return_reason}"
        
        return True, "Return items are valid"
    
    @staticmethod
    def validate_exchange_items(return_items: List[Dict]) -> Tuple[bool, str]:
        """Validate exchange items"""
        
        for return_item in return_items:
            if return_item.get('exchange_product_id'):
                # Validate exchange product exists and is available
                from app import Product
                exchange_product = Product.query.get(return_item['exchange_product_id'])
                if not exchange_product:
                    return False, f"Exchange product {return_item['exchange_product_id']} not found"
                
                if exchange_product.stock_quantity < return_item.get('exchange_quantity', 1):
                    return False, f"Insufficient stock for exchange product {exchange_product.name}"
        
        return True, "Exchange items are valid"

class RMACalculator:
    """RMA calculation utilities"""
    
    @staticmethod
    def calculate_refund_amount(rma_items: List, config) -> Dict[str, Any]:
        """Calculate refund amount for RMA items"""
        
        subtotal = Decimal('0.00')
        for item in rma_items:
            subtotal += Decimal(str(item.total_price))
        
        # Calculate restocking fee
        restocking_fee = Decimal('0.00')
        if config.restocking_fee_percentage > 0:
            restocking_fee = (subtotal * Decimal(str(config.restocking_fee_percentage / 100))).quantize(
                Decimal('0.01'), rounding=ROUND_HALF_UP
            )
        
        # Calculate return shipping cost
        return_shipping_cost = Decimal('0.00')
        if not config.free_return_shipping:
            # This would be calculated based on shipping rules
            return_shipping_cost = Decimal('9.99')  # Default shipping cost
        
        # Calculate final refund amount
        refund_amount = subtotal - restocking_fee - return_shipping_cost
        refund_amount = max(Decimal('0.00'), refund_amount)
        
        return {
            'subtotal': float(subtotal),
            'restocking_fee': float(restocking_fee),
            'return_shipping_cost': float(return_shipping_cost),
            'refund_amount': float(refund_amount),
            'breakdown': {
                'item_total': float(subtotal),
                'fees': {
                    'restocking_fee': float(restocking_fee),
                    'return_shipping': float(return_shipping_cost)
                },
                'final_refund': float(refund_amount)
            }
        }
    
    @staticmethod
    def calculate_exchange_cost_difference(original_items: List, exchange_items: List) -> Dict[str, Any]:
        """Calculate cost difference for exchanges"""
        
        original_total = sum(Decimal(str(item.total_price)) for item in original_items)
        
        exchange_total = Decimal('0.00')
        for exchange_item in exchange_items:
            from app import Product
            product = Product.query.get(exchange_item['product_id'])
            if product:
                exchange_total += Decimal(str(product.price)) * Decimal(str(exchange_item['quantity']))
        
        difference = exchange_total - original_total
        
        return {
            'original_total': float(original_total),
            'exchange_total': float(exchange_total),
            'difference': float(difference),
            'additional_payment_required': difference > 0,
            'refund_due': difference < 0,
            'amount': float(abs(difference))
        }

class RMAReportGenerator:
    """RMA reporting utilities"""
    
    @staticmethod
    def generate_rma_analytics(start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Generate RMA analytics for date range"""
        try:
            from app import db, RMARequest, RMAItem
            
            # Basic statistics
            total_requests = RMARequest.query.filter(
                RMARequest.created_at.between(start_date, end_date)
            ).count()
            
            approved_requests = RMARequest.query.filter(
                RMARequest.created_at.between(start_date, end_date),
                RMARequest.status.in_(['approved', 'refund_completed', 'exchange_completed'])
            ).count()
            
            rejected_requests = RMARequest.query.filter(
                RMARequest.created_at.between(start_date, end_date),
                RMARequest.status == 'rejected'
            ).count()
            
            # Calculate approval rate
            approval_rate = (approved_requests / total_requests * 100) if total_requests > 0 else 0
            
            # Calculate total refund amount
            total_refund_amount = db.session.query(
                db.func.sum(RMARequest.total_refund_amount)
            ).filter(
                RMARequest.created_at.between(start_date, end_date),
                RMARequest.status == 'refund_completed'
            ).scalar() or 0
            
            # Return reason analysis
            return_reasons = db.session.query(
                RMAItem.return_reason,
                db.func.count(RMAItem.id).label('count')
            ).join(RMARequest).filter(
                RMARequest.created_at.between(start_date, end_date)
            ).group_by(RMAItem.return_reason).all()
            
            reason_breakdown = {reason: count for reason, count in return_reasons}
            
            # RMA type breakdown
            rma_types = db.session.query(
                RMARequest.rma_type,
                db.func.count(RMARequest.id).label('count')
            ).filter(
                RMARequest.created_at.between(start_date, end_date)
            ).group_by(RMARequest.rma_type).all()
            
            type_breakdown = {rma_type: count for rma_type, count in rma_types}
            
            return {
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                },
                'summary': {
                    'total_requests': total_requests,
                    'approved_requests': approved_requests,
                    'rejected_requests': rejected_requests,
                    'pending_requests': total_requests - approved_requests - rejected_requests,
                    'approval_rate': round(approval_rate, 2),
                    'total_refund_amount': float(total_refund_amount)
                },
                'breakdown': {
                    'return_reasons': reason_breakdown,
                    'rma_types': type_breakdown
                }
            }
            
        except Exception as e:
            logger.error(f"Error generating RMA analytics: {e}")
            return {
                'error': 'Failed to generate analytics',
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                }
            }

class RMANotificationHelper:
    """RMA notification utilities"""

    @staticmethod
    def send_rma_created_notification(rma_request) -> bool:
        """Send notification when RMA is created"""
        try:
            # This would integrate with notification service
            logger.info(f"RMA created notification sent for {rma_request.rma_number}")
            return True
        except Exception as e:
            logger.error(f"Error sending RMA created notification: {e}")
            return False

    @staticmethod
    def send_rma_approved_notification(rma_request) -> bool:
        """Send notification when RMA is approved"""
        try:
            # This would integrate with notification service
            logger.info(f"RMA approved notification sent for {rma_request.rma_number}")
            return True
        except Exception as e:
            logger.error(f"Error sending RMA approved notification: {e}")
            return False

class RMAIntegrationHelper:
    """Integration utilities for RMA system"""

    @staticmethod
    def sync_with_inventory_system(rma_items: List) -> bool:
        """Sync RMA items with inventory system"""
        try:
            from app import db, Product

            for rma_item in rma_items:
                if rma_item.item_status == 'received' and rma_item.inspection_result == 'passed':
                    # Add back to inventory
                    product = db.session.query(Product).get(rma_item.product_id)
                    if product:
                        product.stock_quantity += rma_item.quantity
                        product.updated_at = datetime.utcnow()

            db.session.commit()
            return True

        except Exception as e:
            logger.error(f"Error syncing with inventory system: {e}")
            db.session.rollback()
            return False

# Export all utility classes
__all__ = [
    'RMAValidator',
    'RMACalculator',
    'RMAReportGenerator',
    'RMANotificationHelper',
    'RMAIntegrationHelper'
]
