"""
Shipping Carrier Integration System
==================================

Comprehensive carrier integration system with support for multiple carriers,
rate calculation, label generation, and tracking APIs.

Supported Carriers:
- FedEx: International and domestic express delivery
- UPS: Global shipping services
- DHL: International express delivery
- Blue Dart: India's leading express delivery
- Delhivery: E-commerce logistics platform
- DTDC: Domestic and international courier
- India Post: Government postal service
"""

import requests
import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Any
from abc import ABC, abstractmethod
import logging
import os

# Import our architecture components
from .order_fulfillment_architecture import (
    ShippingCarrier, ShipmentStatus, TrackingEventType, PackageType,
    Address, Package, ShippingRate, TrackingEvent, FulfillmentConfig
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ============================================================================
# CONFIGURATION AND SECURITY
# ============================================================================

class CarrierConfig:
    """Configuration management for carrier APIs"""

    def __init__(self):
        # Carrier API credentials from environment variables
        self.credentials = {
            'fedex': {
                'api_key': os.environ.get('FEDEX_API_KEY', 'demo_key'),
                'secret_key': os.environ.get('FEDEX_SECRET_KEY', 'demo_secret'),
                'account_number': os.environ.get('FEDEX_ACCOUNT_NUMBER', '*********'),
                'meter_number': os.environ.get('FEDEX_METER_NUMBER', '*********'),
                'sandbox': os.environ.get('FEDEX_SANDBOX', 'true').lower() == 'true'
            },
            'ups': {
                'api_key': os.environ.get('UPS_API_KEY', 'demo_key'),
                'username': os.environ.get('UPS_USERNAME', 'demo_user'),
                'password': os.environ.get('UPS_PASSWORD', 'demo_pass'),
                'account_number': os.environ.get('UPS_ACCOUNT_NUMBER', '123456'),
                'sandbox': os.environ.get('UPS_SANDBOX', 'true').lower() == 'true'
            },
            'dhl': {
                'api_key': os.environ.get('DHL_API_KEY', 'demo_key'),
                'api_secret': os.environ.get('DHL_API_SECRET', 'demo_secret'),
                'account_number': os.environ.get('DHL_ACCOUNT_NUMBER', '*********'),
                'sandbox': os.environ.get('DHL_SANDBOX', 'true').lower() == 'true'
            },

            'delhivery': {
                'api_key': os.environ.get('DELHIVERY_API_KEY', 'demo_key'),
                'sandbox': os.environ.get('DELHIVERY_SANDBOX', 'true').lower() == 'true'
            },
            'shiprocket': {
                'email': os.environ.get('SHIPROCKET_EMAIL', '<EMAIL>'),
                'password': os.environ.get('SHIPROCKET_PASSWORD', 'demo_password'),
                'sandbox': os.environ.get('SHIPROCKET_SANDBOX', 'true').lower() == 'true',
                'default_pickup_location': os.environ.get('SHIPROCKET_DEFAULT_PICKUP_LOCATION', 'primary'),
                'auto_awb': os.environ.get('SHIPROCKET_AUTO_AWB', 'true').lower() == 'true',
                'auto_pickup': os.environ.get('SHIPROCKET_AUTO_PICKUP', 'true').lower() == 'true'
            }
        }

    def get_credentials(self, carrier: str) -> Dict[str, Any]:
        """Get credentials for a carrier"""
        return self.credentials.get(carrier, {})

# ============================================================================
# BASE CARRIER INTERFACE
# ============================================================================

class CarrierAPIError(Exception):
    """Base exception for carrier API errors"""
    def __init__(self, message: str, carrier: str, error_code: str = None):
        self.message = message
        self.carrier = carrier
        self.error_code = error_code
        super().__init__(f"{carrier}: {message}")

class BaseCarrierAPI(ABC):
    """Abstract base class for all carrier API implementations"""
    
    def __init__(self, config: CarrierConfig):
        self.config = config
        self.carrier_name = self.__class__.__name__.replace('API', '').lower()
        self.credentials = config.get_credentials(self.carrier_name)
        self.session = requests.Session()
        self.session.timeout = 30  # 30 second timeout
        
    @abstractmethod
    def calculate_rates(self, origin: Address, destination: Address, 
                       packages: List[Package]) -> List[ShippingRate]:
        """Calculate shipping rates for given origin, destination, and packages"""
        pass
    
    @abstractmethod
    def create_shipment(self, origin: Address, destination: Address,
                       packages: List[Package], service_type: str,
                       reference_number: str = None) -> Dict[str, Any]:
        """Create a shipment and generate shipping label"""
        pass
    
    @abstractmethod
    def track_shipment(self, tracking_number: str) -> List[TrackingEvent]:
        """Track a shipment by tracking number"""
        pass
    
    @abstractmethod
    def cancel_shipment(self, tracking_number: str) -> bool:
        """Cancel a shipment"""
        pass
    
    @abstractmethod
    def schedule_pickup(self, pickup_address: Address, packages: List[Package],
                       pickup_date: datetime, time_window: str = None) -> Dict[str, Any]:
        """Schedule a pickup for packages"""
        pass
    
    def _make_request(self, method: str, url: str, headers: Dict[str, str] = None,
                     data: Dict[str, Any] = None, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Make HTTP request with error handling and logging"""
        try:
            headers = headers or {}
            headers.setdefault('Content-Type', 'application/json')
            headers.setdefault('User-Agent', 'Allora-Fulfillment/1.0')
            
            logger.info(f"Making {method} request to {url}")
            
            response = self.session.request(
                method=method,
                url=url,
                headers=headers,
                json=data if data else None,
                params=params,
                timeout=30
            )
            
            logger.info(f"Response status: {response.status_code}")
            
            if response.status_code >= 400:
                error_msg = f"HTTP {response.status_code}: {response.text}"
                raise CarrierAPIError(error_msg, self.carrier_name, str(response.status_code))
            
            return response.json() if response.content else {}
            
        except requests.exceptions.Timeout:
            raise CarrierAPIError("Request timeout", self.carrier_name, "TIMEOUT")
        except requests.exceptions.ConnectionError:
            raise CarrierAPIError("Connection error", self.carrier_name, "CONNECTION_ERROR")
        except requests.exceptions.RequestException as e:
            raise CarrierAPIError(f"Request failed: {str(e)}", self.carrier_name, "REQUEST_ERROR")
        except json.JSONDecodeError:
            raise CarrierAPIError("Invalid JSON response", self.carrier_name, "INVALID_JSON")

# ============================================================================
# BLUE DART INTEGRATION REMOVED - CONFIGURE ALTERNATIVE CARRIER AS NEEDED
# ============================================================================

# BlueDartAPI class removed - configure alternative shipping carrier



# ============================================================================
# DELHIVERY API IMPLEMENTATION
# ============================================================================

class DelhiveryAPI(BaseCarrierAPI):
    """Delhivery Express API implementation"""

    def __init__(self, config: CarrierConfig):
        super().__init__(config)
        self.base_url = "https://track.delhivery.com/api" if not self.credentials.get('sandbox') else "https://staging-express.delhivery.com/api"

    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for Delhivery API"""
        return {
            'Authorization': f"Token {self.credentials.get('api_key', '')}",
            'Content-Type': 'application/json'
        }

    def calculate_rates(self, origin: Address, destination: Address,
                       packages: List[Package]) -> List[ShippingRate]:
        """Calculate Delhivery shipping rates"""
        try:
            total_weight = sum(pkg.weight for pkg in packages)

            # Delhivery rate calculation
            params = {
                'md': 'S',  # Mode: Surface
                'ss': 'Delivered',  # Service type
                'o_pin': origin.postal_code,
                'd_pin': destination.postal_code,
                'cgm': total_weight * 1000,  # Convert to grams
                'pt': 'Pre-paid'  # Payment type
            }

            url = f"{self.base_url}/kinko/v1/invoice/charges"
            response = self._make_request('GET', url, self._get_auth_headers(), params=params)

            rates = []
            if response.get('delivery_charges'):
                base_rate = float(response['delivery_charges'])

                # Surface delivery
                rates.append(ShippingRate(
                    carrier=ShippingCarrier.DELHIVERY,
                    service_type='Surface',
                    rate=base_rate,
                    currency='INR',
                    estimated_days=5,
                    guaranteed=False,
                    pickup_required=True,
                    signature_required=False,
                    insurance_included=False,
                    tracking_included=True
                ))

                # Express delivery
                rates.append(ShippingRate(
                    carrier=ShippingCarrier.DELHIVERY,
                    service_type='Express',
                    rate=base_rate * 1.3,
                    currency='INR',
                    estimated_days=3,
                    guaranteed=False,
                    pickup_required=True,
                    signature_required=False,
                    insurance_included=False,
                    tracking_included=True
                ))

            return rates

        except Exception as e:
            logger.error(f"Delhivery rate calculation error: {str(e)}")
            raise CarrierAPIError(f"Rate calculation failed: {str(e)}", "delhivery")

    def create_shipment(self, origin: Address, destination: Address,
                       packages: List[Package], service_type: str,
                       reference_number: str = None) -> Dict[str, Any]:
        """Create Delhivery shipment"""
        try:
            total_weight = sum(pkg.weight for pkg in packages)
            total_value = sum(pkg.declared_value for pkg in packages)

            # Generate unique waybill number
            waybill = f"DL{datetime.now().strftime('%Y%m%d%H%M%S')}"

            payload = {
                'format': 'json',
                'data': json.dumps({
                    'shipments': [{
                        'name': destination.name,
                        'add': f"{destination.address_line_1}, {destination.address_line_2 or ''}",
                        'pin': destination.postal_code,
                        'city': destination.city,
                        'state': destination.state,
                        'country': destination.country,
                        'phone': destination.phone or '',
                        'order': reference_number or waybill,
                        'payment_mode': 'Prepaid',
                        'return_pin': origin.postal_code,
                        'return_city': origin.city,
                        'return_phone': origin.phone or '',
                        'return_add': f"{origin.address_line_1}, {origin.address_line_2 or ''}",
                        'return_state': origin.state,
                        'return_country': origin.country,
                        'products_desc': 'E-commerce Products',
                        'hsn_code': '',
                        'cod_amount': '0',
                        'order_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        'total_amount': str(total_value),
                        'seller_add': f"{origin.address_line_1}, {origin.address_line_2 or ''}",
                        'seller_name': origin.name,
                        'seller_inv': reference_number or waybill,
                        'quantity': str(len(packages)),
                        'waybill': waybill,
                        'shipment_width': str(max(pkg.width for pkg in packages)),
                        'shipment_height': str(max(pkg.height for pkg in packages)),
                        'weight': str(total_weight),
                        'seller_gst_tin': '',
                        'shipping_mode': 'Surface',
                        'address_type': 'home'
                    }]
                })
            }

            url = f"{self.base_url}/cmu/create.json"
            response = self._make_request('POST', url, self._get_auth_headers(), payload)

            if response.get('success'):
                return {
                    'success': True,
                    'tracking_number': waybill,
                    'label_url': f"{self.base_url}/api/p/packing_slip?wbns={waybill}&pdf=true",
                    'carrier_reference': waybill,
                    'estimated_delivery': self._calculate_delivery_date(service_type),
                    'shipping_cost': 0  # Will be calculated separately
                }
            else:
                raise CarrierAPIError("Shipment creation failed", "delhivery")

        except Exception as e:
            logger.error(f"Delhivery shipment creation error: {str(e)}")
            raise CarrierAPIError(f"Shipment creation failed: {str(e)}", "delhivery")

    def track_shipment(self, tracking_number: str) -> List[TrackingEvent]:
        """Track Delhivery shipment"""
        try:
            params = {
                'waybill': tracking_number,
                'verbose': '3'
            }

            url = f"{self.base_url}/v1/packages/json"
            response = self._make_request('GET', url, self._get_auth_headers(), params=params)

            events = []
            if response.get('ShipmentData'):
                for shipment in response['ShipmentData']:
                    for scan in shipment.get('Scans', []):
                        events.append(TrackingEvent(
                            event_type=self._map_status_to_event_type(scan.get('ScanDetail', {}).get('ScanType', '')),
                            status=scan.get('ScanDetail', {}).get('ScanType', ''),
                            description=scan.get('ScanDetail', {}).get('Instructions', ''),
                            location=scan.get('ScanDetail', {}).get('ScannedLocation', ''),
                            timestamp=datetime.strptime(scan.get('ScanDetail', {}).get('ScanDateTime', ''), '%Y-%m-%d %H:%M:%S'),
                            carrier_code='DL'
                        ))

            return events

        except Exception as e:
            logger.error(f"Delhivery tracking error: {str(e)}")
            raise CarrierAPIError(f"Tracking failed: {str(e)}", "delhivery")

    def cancel_shipment(self, tracking_number: str) -> bool:
        """Cancel Delhivery shipment"""
        try:
            payload = {
                'waybill': tracking_number,
                'cancellation': 'true'
            }

            url = f"{self.base_url}/p/edit"
            response = self._make_request('POST', url, self._get_auth_headers(), payload)

            return response.get('success', False)

        except Exception as e:
            logger.error(f"Delhivery cancellation error: {str(e)}")
            return False

    def schedule_pickup(self, pickup_address: Address, packages: List[Package],
                       pickup_date: datetime, time_window: str = None) -> Dict[str, Any]:
        """Schedule Delhivery pickup"""
        try:
            payload = {
                'pickup_location': {
                    'name': pickup_address.name,
                    'add': f"{pickup_address.address_line_1}, {pickup_address.address_line_2 or ''}",
                    'city': pickup_address.city,
                    'state': pickup_address.state,
                    'country': pickup_address.country,
                    'pin': pickup_address.postal_code,
                    'phone': pickup_address.phone or ''
                },
                'pickup_date': pickup_date.strftime('%Y-%m-%d'),
                'pickup_time': time_window or '10:00-18:00',
                'expected_package_count': len(packages)
            }

            url = f"{self.base_url}/fm/request/new"
            response = self._make_request('POST', url, self._get_auth_headers(), payload)

            if response.get('success'):
                return {
                    'success': True,
                    'pickup_id': response.get('data', {}).get('pickup_id'),
                    'pickup_date': pickup_date.date(),
                    'time_window': time_window or '10:00-18:00'
                }
            else:
                raise CarrierAPIError("Pickup scheduling failed", "delhivery")

        except Exception as e:
            logger.error(f"Delhivery pickup scheduling error: {str(e)}")
            raise CarrierAPIError(f"Pickup scheduling failed: {str(e)}", "delhivery")

    def _map_status_to_event_type(self, status: str) -> TrackingEventType:
        """Map Delhivery status to standard tracking event type"""
        status_mapping = {
            'Booked': TrackingEventType.LABEL_CREATED,
            'Pickup': TrackingEventType.PICKED_UP,
            'In-Transit': TrackingEventType.IN_TRANSIT,
            'Dispatched': TrackingEventType.IN_TRANSIT,
            'Out-for-Delivery': TrackingEventType.OUT_FOR_DELIVERY,
            'Delivered': TrackingEventType.DELIVERED,
            'Exception': TrackingEventType.EXCEPTION,
            'RTO': TrackingEventType.RETURNED
        }
        return status_mapping.get(status, TrackingEventType.IN_TRANSIT)

    def _calculate_delivery_date(self, service_type: str) -> datetime:
        """Calculate estimated delivery date"""
        days = 3 if service_type == 'Express' else 5
        return datetime.now() + timedelta(days=days)

# ============================================================================
# FEDEX API IMPLEMENTATION
# ============================================================================

class FedExAPI(BaseCarrierAPI):
    """FedEx Express API implementation"""

    def __init__(self, config: CarrierConfig):
        super().__init__(config)
        self.base_url = "https://apis.fedex.com" if not self.credentials.get('sandbox') else "https://apis-sandbox.fedex.com"
        self.access_token = None
        self.token_expires_at = None

    def _get_access_token(self) -> str:
        """Get OAuth access token for FedEx API"""
        if self.access_token and self.token_expires_at and datetime.now() < self.token_expires_at:
            return self.access_token

        try:
            payload = {
                'grant_type': 'client_credentials',
                'client_id': self.credentials.get('api_key', ''),
                'client_secret': self.credentials.get('secret_key', '')
            }

            headers = {
                'Content-Type': 'application/x-www-form-urlencoded'
            }

            url = f"{self.base_url}/oauth/token"
            response = requests.post(url, data=payload, headers=headers, timeout=30)

            if response.status_code == 200:
                token_data = response.json()
                self.access_token = token_data['access_token']
                expires_in = token_data.get('expires_in', 3600)
                self.token_expires_at = datetime.now() + timedelta(seconds=expires_in - 300)  # 5 min buffer
                return self.access_token
            else:
                raise CarrierAPIError("Failed to get access token", "fedex")

        except Exception as e:
            logger.error(f"FedEx token error: {str(e)}")
            raise CarrierAPIError(f"Authentication failed: {str(e)}", "fedex")

    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for FedEx API"""
        return {
            'Authorization': f'Bearer {self._get_access_token()}',
            'Content-Type': 'application/json',
            'X-locale': 'en_US'
        }

    def calculate_rates(self, origin: Address, destination: Address,
                       packages: List[Package]) -> List[ShippingRate]:
        """Calculate FedEx shipping rates"""
        try:
            payload = {
                'accountNumber': {
                    'value': self.credentials.get('account_number', '')
                },
                'requestedShipment': {
                    'shipper': {
                        'address': {
                            'postalCode': origin.postal_code,
                            'countryCode': self._get_country_code(origin.country),
                            'residential': False
                        }
                    },
                    'recipient': {
                        'address': {
                            'postalCode': destination.postal_code,
                            'countryCode': self._get_country_code(destination.country),
                            'residential': True
                        }
                    },
                    'pickupType': 'USE_SCHEDULED_PICKUP',
                    'serviceType': 'FEDEX_GROUND',
                    'packagingType': 'YOUR_PACKAGING',
                    'requestedPackageLineItems': [{
                        'weight': {
                            'units': 'KG',
                            'value': pkg.weight
                        },
                        'dimensions': {
                            'length': int(pkg.length),
                            'width': int(pkg.width),
                            'height': int(pkg.height),
                            'units': 'CM'
                        }
                    } for pkg in packages]
                }
            }

            url = f"{self.base_url}/rate/v1/rates/quotes"
            response = self._make_request('POST', url, self._get_auth_headers(), payload)

            rates = []
            if response.get('output', {}).get('rateReplyDetails'):
                for rate_detail in response['output']['rateReplyDetails']:
                    service_type = rate_detail.get('serviceType', '')
                    rate_details = rate_detail.get('ratedShipmentDetails', [{}])[0]
                    total_charges = rate_details.get('totalNetCharge', {})

                    rates.append(ShippingRate(
                        carrier=ShippingCarrier.FEDEX,
                        service_type=service_type,
                        rate=float(total_charges.get('amount', 0)),
                        currency=total_charges.get('currency', 'USD'),
                        estimated_days=self._get_transit_days(service_type),
                        guaranteed=service_type in ['FEDEX_EXPRESS_SAVER', 'PRIORITY_OVERNIGHT'],
                        pickup_required=True,
                        signature_required=False,
                        insurance_included=False,
                        tracking_included=True
                    ))

            return rates

        except Exception as e:
            logger.error(f"FedEx rate calculation error: {str(e)}")
            raise CarrierAPIError(f"Rate calculation failed: {str(e)}", "fedex")

    def create_shipment(self, origin: Address, destination: Address,
                       packages: List[Package], service_type: str,
                       reference_number: str = None) -> Dict[str, Any]:
        """Create FedEx shipment"""
        try:
            payload = {
                'labelResponseOptions': 'URL_ONLY',
                'requestedShipment': {
                    'shipper': {
                        'contact': {
                            'personName': origin.name,
                            'phoneNumber': origin.phone or ''
                        },
                        'address': {
                            'streetLines': [origin.address_line_1, origin.address_line_2 or ''],
                            'city': origin.city,
                            'stateOrProvinceCode': origin.state,
                            'postalCode': origin.postal_code,
                            'countryCode': self._get_country_code(origin.country)
                        }
                    },
                    'recipients': [{
                        'contact': {
                            'personName': destination.name,
                            'phoneNumber': destination.phone or ''
                        },
                        'address': {
                            'streetLines': [destination.address_line_1, destination.address_line_2 or ''],
                            'city': destination.city,
                            'stateOrProvinceCode': destination.state,
                            'postalCode': destination.postal_code,
                            'countryCode': self._get_country_code(destination.country),
                            'residential': True
                        }
                    }],
                    'shipDatestamp': datetime.now().strftime('%Y-%m-%d'),
                    'serviceType': service_type or 'FEDEX_GROUND',
                    'packagingType': 'YOUR_PACKAGING',
                    'pickupType': 'USE_SCHEDULED_PICKUP',
                    'blockInsightVisibility': False,
                    'shippingChargesPayment': {
                        'paymentType': 'SENDER',
                        'payor': {
                            'responsibleParty': {
                                'accountNumber': {
                                    'value': self.credentials.get('account_number', '')
                                }
                            }
                        }
                    },
                    'labelSpecification': {
                        'imageType': 'PDF',
                        'labelStockType': 'PAPER_4X6'
                    },
                    'requestedPackageLineItems': [{
                        'weight': {
                            'units': 'KG',
                            'value': pkg.weight
                        },
                        'dimensions': {
                            'length': int(pkg.length),
                            'width': int(pkg.width),
                            'height': int(pkg.height),
                            'units': 'CM'
                        }
                    } for pkg in packages]
                }
            }

            url = f"{self.base_url}/ship/v1/shipments"
            response = self._make_request('POST', url, self._get_auth_headers(), payload)

            if response.get('output', {}).get('transactionShipments'):
                shipment_data = response['output']['transactionShipments'][0]
                completed_shipment = shipment_data.get('completedShipmentDetail', {})

                return {
                    'success': True,
                    'tracking_number': completed_shipment.get('masterTrackingId', {}).get('trackingNumber'),
                    'label_url': shipment_data.get('pieceResponses', [{}])[0].get('packageDocuments', [{}])[0].get('url'),
                    'carrier_reference': completed_shipment.get('shipmentRating', {}).get('actualRateType'),
                    'estimated_delivery': self._calculate_delivery_date(service_type),
                    'shipping_cost': float(completed_shipment.get('shipmentRating', {}).get('totalNetCharge', {}).get('amount', 0))
                }
            else:
                raise CarrierAPIError("Shipment creation failed", "fedex")

        except Exception as e:
            logger.error(f"FedEx shipment creation error: {str(e)}")
            raise CarrierAPIError(f"Shipment creation failed: {str(e)}", "fedex")

    def track_shipment(self, tracking_number: str) -> List[TrackingEvent]:
        """Track FedEx shipment"""
        try:
            payload = {
                'includeDetailedScans': True,
                'trackingInfo': [{
                    'trackingNumberInfo': {
                        'trackingNumber': tracking_number
                    }
                }]
            }

            url = f"{self.base_url}/track/v1/trackingnumbers"
            response = self._make_request('POST', url, self._get_auth_headers(), payload)

            events = []
            if response.get('output', {}).get('completeTrackResults'):
                for track_result in response['output']['completeTrackResults']:
                    for track_detail in track_result.get('trackResults', []):
                        for scan_event in track_detail.get('scanEvents', []):
                            events.append(TrackingEvent(
                                event_type=self._map_status_to_event_type(scan_event.get('eventType', '')),
                                status=scan_event.get('eventDescription', ''),
                                description=scan_event.get('eventDescription', ''),
                                location=f"{scan_event.get('scanLocation', {}).get('city', '')}, {scan_event.get('scanLocation', {}).get('stateOrProvinceCode', '')}",
                                timestamp=datetime.strptime(scan_event.get('date', ''), '%Y-%m-%dT%H:%M:%S'),
                                carrier_code='FX'
                            ))

            return events

        except Exception as e:
            logger.error(f"FedEx tracking error: {str(e)}")
            raise CarrierAPIError(f"Tracking failed: {str(e)}", "fedex")

    def cancel_shipment(self, tracking_number: str) -> bool:
        """Cancel FedEx shipment"""
        try:
            payload = {
                'accountNumber': {
                    'value': self.credentials.get('account_number', '')
                },
                'trackingNumber': tracking_number,
                'deletionControl': 'DELETE_ALL_PACKAGES'
            }

            url = f"{self.base_url}/ship/v1/shipments/cancel"
            response = self._make_request('PUT', url, self._get_auth_headers(), payload)

            return response.get('output', {}).get('cancelledShipment', False)

        except Exception as e:
            logger.error(f"FedEx cancellation error: {str(e)}")
            return False

    def schedule_pickup(self, pickup_address: Address, packages: List[Package],
                       pickup_date: datetime, time_window: str = None) -> Dict[str, Any]:
        """Schedule FedEx pickup"""
        try:
            payload = {
                'associatedAccountNumber': {
                    'value': self.credentials.get('account_number', '')
                },
                'originDetail': {
                    'pickupLocation': {
                        'contact': {
                            'personName': pickup_address.name,
                            'phoneNumber': pickup_address.phone or ''
                        },
                        'address': {
                            'streetLines': [pickup_address.address_line_1, pickup_address.address_line_2 or ''],
                            'city': pickup_address.city,
                            'stateOrProvinceCode': pickup_address.state,
                            'postalCode': pickup_address.postal_code,
                            'countryCode': self._get_country_code(pickup_address.country)
                        }
                    },
                    'readyDateTime': pickup_date.strftime('%Y-%m-%dT09:00:00'),
                    'companyCloseTime': '18:00:00'
                },
                'packageCount': len(packages),
                'totalWeight': {
                    'units': 'KG',
                    'value': sum(pkg.weight for pkg in packages)
                },
                'carrierCode': 'FDXE'
            }

            url = f"{self.base_url}/pickup/v1/pickups"
            response = self._make_request('POST', url, self._get_auth_headers(), payload)

            if response.get('output'):
                return {
                    'success': True,
                    'pickup_id': response['output'].get('pickupConfirmationCode'),
                    'pickup_date': pickup_date.date(),
                    'time_window': time_window or '09:00-18:00'
                }
            else:
                raise CarrierAPIError("Pickup scheduling failed", "fedex")

        except Exception as e:
            logger.error(f"FedEx pickup scheduling error: {str(e)}")
            raise CarrierAPIError(f"Pickup scheduling failed: {str(e)}", "fedex")

    def _get_country_code(self, country: str) -> str:
        """Get ISO country code"""
        country_codes = {
            'India': 'IN',
            'United States': 'US',
            'Canada': 'CA',
            'United Kingdom': 'GB',
            'Australia': 'AU'
        }
        return country_codes.get(country, 'IN')

    def _get_transit_days(self, service_type: str) -> int:
        """Get estimated transit days for service type"""
        transit_days = {
            'PRIORITY_OVERNIGHT': 1,
            'FEDEX_EXPRESS_SAVER': 3,
            'FEDEX_GROUND': 5,
            'INTERNATIONAL_ECONOMY': 7,
            'INTERNATIONAL_PRIORITY': 3
        }
        return transit_days.get(service_type, 5)

    def _map_status_to_event_type(self, status: str) -> TrackingEventType:
        """Map FedEx status to standard tracking event type"""
        status_mapping = {
            'SH': TrackingEventType.LABEL_CREATED,
            'PU': TrackingEventType.PICKED_UP,
            'IT': TrackingEventType.IN_TRANSIT,
            'OD': TrackingEventType.OUT_FOR_DELIVERY,
            'DL': TrackingEventType.DELIVERED,
            'EX': TrackingEventType.EXCEPTION,
            'RT': TrackingEventType.RETURNED
        }
        return status_mapping.get(status, TrackingEventType.IN_TRANSIT)

    def _calculate_delivery_date(self, service_type: str) -> datetime:
        """Calculate estimated delivery date"""
        days = self._get_transit_days(service_type)
        return datetime.now() + timedelta(days=days)

# ============================================================================
# CARRIER FACTORY AND RATE CALCULATION ENGINE
# ============================================================================

class CarrierFactory:
    """Factory class for creating carrier API instances"""

    def __init__(self):
        self.config = CarrierConfig()
        self._carriers = {}

    def get_carrier(self, carrier: ShippingCarrier) -> BaseCarrierAPI:
        """Get carrier API instance"""
        if carrier not in self._carriers:
            if carrier == ShippingCarrier.SHIPROCKET:
                from .shiprocket_api import ShiprocketAPI
                self._carriers[carrier] = ShiprocketAPI(self.config)
            elif carrier == ShippingCarrier.DELHIVERY:
                self._carriers[carrier] = DelhiveryAPI(self.config)
            elif carrier == ShippingCarrier.FEDEX:
                self._carriers[carrier] = FedExAPI(self.config)
            # Add other carriers as needed
            else:
                raise ValueError(f"Unsupported carrier: {carrier}")

        return self._carriers[carrier]

    def get_all_carriers(self) -> List[BaseCarrierAPI]:
        """Get all available carrier instances"""
        available_carriers = [
            ShippingCarrier.SHIPROCKET,
            ShippingCarrier.DELHIVERY,
            ShippingCarrier.FEDEX
        ]
        return [self.get_carrier(carrier) for carrier in available_carriers]

class RateCalculationEngine:
    """Engine for calculating and comparing shipping rates across carriers"""

    def __init__(self):
        self.factory = CarrierFactory()
        self.cache = {}  # Simple in-memory cache
        self.cache_ttl = 300  # 5 minutes

    def calculate_rates(self, origin: Address, destination: Address,
                       packages: List[Package], carriers: List[ShippingCarrier] = None) -> List[ShippingRate]:
        """Calculate rates from multiple carriers"""
        if not carriers:
            carriers = [ShippingCarrier.SHIPROCKET, ShippingCarrier.DELHIVERY, ShippingCarrier.FEDEX]

        # Generate cache key
        cache_key = self._generate_cache_key(origin, destination, packages, carriers)

        # Check cache
        if cache_key in self.cache:
            cached_data = self.cache[cache_key]
            if datetime.now() - cached_data['timestamp'] < timedelta(seconds=self.cache_ttl):
                logger.info("Returning cached rates")
                return cached_data['rates']

        all_rates = []

        for carrier in carriers:
            try:
                carrier_api = self.factory.get_carrier(carrier)
                rates = carrier_api.calculate_rates(origin, destination, packages)
                all_rates.extend(rates)
                logger.info(f"Got {len(rates)} rates from {carrier.value}")
            except CarrierAPIError as e:
                logger.error(f"Failed to get rates from {carrier.value}: {e}")
                continue
            except Exception as e:
                logger.error(f"Unexpected error from {carrier.value}: {e}")
                continue

        # Cache the results
        self.cache[cache_key] = {
            'rates': all_rates,
            'timestamp': datetime.now()
        }

        # Sort by rate (cheapest first)
        all_rates.sort(key=lambda x: x.rate)

        return all_rates

    def get_best_rate(self, origin: Address, destination: Address,
                     packages: List[Package], criteria: str = 'cheapest') -> ShippingRate:
        """Get the best rate based on criteria"""
        rates = self.calculate_rates(origin, destination, packages)

        if not rates:
            raise CarrierAPIError("No rates available", "rate_engine")

        if criteria == 'cheapest':
            return min(rates, key=lambda x: x.rate)
        elif criteria == 'fastest':
            return min(rates, key=lambda x: x.estimated_days)
        elif criteria == 'most_reliable':
            # Prefer carriers with guaranteed delivery
            guaranteed_rates = [r for r in rates if r.guaranteed]
            if guaranteed_rates:
                return min(guaranteed_rates, key=lambda x: x.rate)
            return min(rates, key=lambda x: x.rate)
        else:
            return rates[0]

    def _generate_cache_key(self, origin: Address, destination: Address,
                           packages: List[Package], carriers: List[ShippingCarrier]) -> str:
        """Generate cache key for rate calculation"""
        key_data = {
            'origin': f"{origin.postal_code}-{origin.country}",
            'destination': f"{destination.postal_code}-{destination.country}",
            'packages': [(pkg.weight, pkg.length, pkg.width, pkg.height) for pkg in packages],
            'carriers': [c.value for c in carriers]
        }
        return hashlib.md5(json.dumps(key_data, sort_keys=True).encode()).hexdigest()

class ShipmentManager:
    """Manager for creating and managing shipments across carriers"""

    def __init__(self):
        self.factory = CarrierFactory()
        self.rate_engine = RateCalculationEngine()

    def create_shipment(self, origin: Address, destination: Address,
                       packages: List[Package], carrier: ShippingCarrier = None,
                       service_type: str = None, reference_number: str = None) -> Dict[str, Any]:
        """Create shipment with specified or optimal carrier"""
        try:
            # If no carrier specified, find the best one
            if not carrier:
                best_rate = self.rate_engine.get_best_rate(origin, destination, packages)
                carrier = best_rate.carrier
                service_type = service_type or best_rate.service_type

            # Create shipment
            carrier_api = self.factory.get_carrier(carrier)
            result = carrier_api.create_shipment(origin, destination, packages, service_type, reference_number)

            logger.info(f"Created shipment with {carrier.value}: {result.get('tracking_number')}")
            return result

        except Exception as e:
            logger.error(f"Shipment creation failed: {str(e)}")
            raise CarrierAPIError(f"Shipment creation failed: {str(e)}", "shipment_manager")

    def track_shipment(self, tracking_number: str, carrier: ShippingCarrier) -> List[TrackingEvent]:
        """Track shipment by tracking number and carrier"""
        try:
            carrier_api = self.factory.get_carrier(carrier)
            events = carrier_api.track_shipment(tracking_number)

            logger.info(f"Retrieved {len(events)} tracking events for {tracking_number}")
            return events

        except Exception as e:
            logger.error(f"Tracking failed: {str(e)}")
            raise CarrierAPIError(f"Tracking failed: {str(e)}", "shipment_manager")

    def cancel_shipment(self, tracking_number: str, carrier: ShippingCarrier) -> bool:
        """Cancel shipment"""
        try:
            carrier_api = self.factory.get_carrier(carrier)
            result = carrier_api.cancel_shipment(tracking_number)

            logger.info(f"Cancellation result for {tracking_number}: {result}")
            return result

        except Exception as e:
            logger.error(f"Cancellation failed: {str(e)}")
            return False

    def schedule_pickup(self, pickup_address: Address, packages: List[Package],
                       pickup_date: datetime, carrier: ShippingCarrier,
                       time_window: str = None) -> Dict[str, Any]:
        """Schedule pickup with carrier"""
        try:
            carrier_api = self.factory.get_carrier(carrier)
            result = carrier_api.schedule_pickup(pickup_address, packages, pickup_date, time_window)

            logger.info(f"Scheduled pickup with {carrier.value}: {result.get('pickup_id')}")
            return result

        except Exception as e:
            logger.error(f"Pickup scheduling failed: {str(e)}")
            raise CarrierAPIError(f"Pickup scheduling failed: {str(e)}", "shipment_manager")
