<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tracking Dashboard - <PERSON>ora Admin</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .status-delivered { background-color: #10b981; color: white; }
        .status-in-transit { background-color: #3b82f6; color: white; }
        .status-delayed { background-color: #f59e0b; color: white; }
        .status-exception { background-color: #ef4444; color: white; }
        .status-pending { background-color: #6b7280; color: white; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <h1 class="text-2xl font-bold text-gray-900">Tracking Dashboard</h1>
                    <div class="flex items-center space-x-4">
                        <select id="timeRange" class="border border-gray-300 rounded-md px-3 py-2">
                            <option value="7">Last 7 days</option>
                            <option value="30" selected>Last 30 days</option>
                            <option value="90">Last 90 days</option>
                        </select>
                        <button id="refreshBtn" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                            Refresh
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Metrics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="metric-card rounded-lg p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm opacity-80">Total Shipments</p>
                            <p id="totalShipments" class="text-3xl font-bold">-</p>
                        </div>
                        <div class="text-4xl opacity-60">📦</div>
                    </div>
                </div>
                
                <div class="metric-card rounded-lg p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm opacity-80">Active Shipments</p>
                            <p id="activeShipments" class="text-3xl font-bold">-</p>
                        </div>
                        <div class="text-4xl opacity-60">🚚</div>
                    </div>
                </div>
                
                <div class="metric-card rounded-lg p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm opacity-80">Delivered</p>
                            <p id="deliveredShipments" class="text-3xl font-bold">-</p>
                        </div>
                        <div class="text-4xl opacity-60">✅</div>
                    </div>
                </div>
                
                <div class="metric-card rounded-lg p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm opacity-80">Exceptions</p>
                            <p id="exceptionShipments" class="text-3xl font-bold">-</p>
                        </div>
                        <div class="text-4xl opacity-60">⚠️</div>
                    </div>
                </div>
            </div>

            <!-- Performance Metrics -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold mb-4">Delivery Performance</h3>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Average Delivery Time</span>
                            <span id="avgDeliveryTime" class="font-semibold">- days</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">On-Time Delivery Rate</span>
                            <span id="onTimeRate" class="font-semibold">-%</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Delayed Shipments</span>
                            <span id="delayedShipments" class="font-semibold text-orange-600">-</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold mb-4">Carrier Performance</h3>
                    <div id="carrierPerformance" class="space-y-3">
                        <!-- Carrier performance will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Charts -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold mb-4">Daily Shipment Volume</h3>
                    <canvas id="volumeChart" width="400" height="200"></canvas>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold mb-4">Status Distribution</h3>
                    <canvas id="statusChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- Recent Exceptions -->
            <div class="bg-white rounded-lg shadow mb-8">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold">Recent Exceptions</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tracking Number</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Carrier</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                            </tr>
                        </thead>
                        <tbody id="exceptionsTable" class="bg-white divide-y divide-gray-200">
                            <!-- Exceptions will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Shipments Table -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-semibold">All Shipments</h3>
                    <div class="flex space-x-2">
                        <select id="filterType" class="border border-gray-300 rounded-md px-3 py-2">
                            <option value="all">All Shipments</option>
                            <option value="active">Active</option>
                            <option value="delivered">Delivered</option>
                            <option value="delayed">Delayed</option>
                            <option value="exceptions">Exceptions</option>
                        </select>
                        <input type="text" id="searchInput" placeholder="Search..." class="border border-gray-300 rounded-md px-3 py-2">
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tracking Number</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Carrier</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Days in Transit</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="shipmentsTable" class="bg-white divide-y divide-gray-200">
                            <!-- Shipments will be loaded here -->
                        </tbody>
                    </table>
                </div>
                <div id="pagination" class="px-6 py-4 border-t border-gray-200 flex justify-between items-center">
                    <!-- Pagination will be loaded here -->
                </div>
            </div>
        </main>
    </div>

    <script>
        // Dashboard JavaScript
        let volumeChart, statusChart;
        let currentPage = 1;
        const itemsPerPage = 50;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardData();
            setupEventListeners();
            
            // Auto-refresh every 30 seconds
            setInterval(loadDashboardData, 30000);
        });

        function setupEventListeners() {
            document.getElementById('refreshBtn').addEventListener('click', loadDashboardData);
            document.getElementById('timeRange').addEventListener('change', loadDashboardData);
            document.getElementById('filterType').addEventListener('change', loadShipments);
            document.getElementById('searchInput').addEventListener('input', debounce(loadShipments, 500));
        }

        async function loadDashboardData() {
            try {
                const days = document.getElementById('timeRange').value;
                const response = await fetch(`/admin/tracking/api/metrics?days=${days}`);
                const data = await response.json();
                
                updateMetrics(data);
                updateCharts(data);
                loadShipments();
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }

        function updateMetrics(data) {
            document.getElementById('totalShipments').textContent = data.total_shipments || 0;
            document.getElementById('activeShipments').textContent = data.active_shipments || 0;
            document.getElementById('deliveredShipments').textContent = data.delivered_shipments || 0;
            document.getElementById('exceptionShipments').textContent = data.exception_shipments || 0;
            document.getElementById('avgDeliveryTime').textContent = `${data.average_delivery_time || 0} days`;
            document.getElementById('onTimeRate').textContent = `${data.on_time_delivery_rate || 0}%`;
            document.getElementById('delayedShipments').textContent = data.delayed_shipments || 0;
            
            updateCarrierPerformance(data.carrier_performance || {});
            updateRecentExceptions(data.recent_exceptions || []);
        }

        function updateCarrierPerformance(carriers) {
            const container = document.getElementById('carrierPerformance');
            container.innerHTML = '';
            
            Object.entries(carriers).forEach(([carrier, data]) => {
                const div = document.createElement('div');
                div.className = 'flex justify-between items-center';
                div.innerHTML = `
                    <span class="text-gray-600 capitalize">${carrier.replace('_', ' ')}</span>
                    <div class="text-right">
                        <div class="font-semibold">${data.delivery_rate}%</div>
                        <div class="text-xs text-gray-500">${data.total_shipments} shipments</div>
                    </div>
                `;
                container.appendChild(div);
            });
        }

        function updateRecentExceptions(exceptions) {
            const tbody = document.getElementById('exceptionsTable');
            tbody.innerHTML = '';
            
            exceptions.forEach(exception => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${exception.order_id}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${exception.tracking_number}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="status-badge status-exception">${exception.status}</span>
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-500">${exception.description || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 uppercase">${exception.carrier}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${new Date(exception.timestamp).toLocaleString()}</td>
                `;
                tbody.appendChild(row);
            });
        }

        function updateCharts(data) {
            // Volume Chart
            if (volumeChart) volumeChart.destroy();
            const volumeCtx = document.getElementById('volumeChart').getContext('2d');
            volumeChart = new Chart(volumeCtx, {
                type: 'line',
                data: {
                    labels: data.daily_shipment_volume.map(d => d.date),
                    datasets: [{
                        label: 'Shipments',
                        data: data.daily_shipment_volume.map(d => d.shipments),
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Status Chart
            if (statusChart) statusChart.destroy();
            const statusCtx = document.getElementById('statusChart').getContext('2d');
            statusChart = new Chart(statusCtx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(data.status_distribution),
                    datasets: [{
                        data: Object.values(data.status_distribution),
                        backgroundColor: [
                            '#10b981', '#3b82f6', '#f59e0b', '#ef4444', '#6b7280'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }

        async function loadShipments() {
            try {
                const filter = document.getElementById('filterType').value;
                const search = document.getElementById('searchInput').value;
                
                const params = new URLSearchParams({
                    filter: filter,
                    page: currentPage,
                    per_page: itemsPerPage
                });
                
                if (search) params.append('search', search);
                
                const response = await fetch(`/admin/tracking/api/shipments?${params}`);
                const data = await response.json();
                
                updateShipmentsTable(data.shipments);
                updatePagination(data);
            } catch (error) {
                console.error('Error loading shipments:', error);
            }
        }

        function updateShipmentsTable(shipments) {
            const tbody = document.getElementById('shipmentsTable');
            tbody.innerHTML = '';
            
            shipments.forEach(shipment => {
                const row = document.createElement('tr');
                const statusClass = getStatusClass(shipment.current_status);
                
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${shipment.order_id}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${shipment.tracking_number}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="status-badge ${statusClass}">${shipment.current_status}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 uppercase">${shipment.carrier_code}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${new Date(shipment.created_date).toLocaleDateString()}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${shipment.days_in_transit}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${shipment.customer_email || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="viewShipmentDetails(${shipment.order_id})" class="text-blue-600 hover:text-blue-900">View</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function getStatusClass(status) {
            const statusMap = {
                'delivered': 'status-delivered',
                'in_transit': 'status-in-transit',
                'out_for_delivery': 'status-in-transit',
                'picked_up': 'status-in-transit',
                'exception': 'status-exception',
                'lost': 'status-exception',
                'damaged': 'status-exception',
                'delayed': 'status-delayed'
            };
            return statusMap[status.toLowerCase()] || 'status-pending';
        }

        function updatePagination(data) {
            const container = document.getElementById('pagination');
            const totalPages = data.total_pages;
            
            container.innerHTML = `
                <div class="text-sm text-gray-700">
                    Showing ${((data.page - 1) * data.per_page) + 1} to ${Math.min(data.page * data.per_page, data.total_count)} of ${data.total_count} results
                </div>
                <div class="flex space-x-2">
                    <button onclick="changePage(${data.page - 1})" ${data.page <= 1 ? 'disabled' : ''} 
                            class="px-3 py-1 border rounded ${data.page <= 1 ? 'bg-gray-100 text-gray-400' : 'bg-white text-gray-700 hover:bg-gray-50'}">
                        Previous
                    </button>
                    <span class="px-3 py-1 bg-blue-600 text-white rounded">
                        ${data.page} of ${totalPages}
                    </span>
                    <button onclick="changePage(${data.page + 1})" ${data.page >= totalPages ? 'disabled' : ''} 
                            class="px-3 py-1 border rounded ${data.page >= totalPages ? 'bg-gray-100 text-gray-400' : 'bg-white text-gray-700 hover:bg-gray-50'}">
                        Next
                    </button>
                </div>
            `;
        }

        function changePage(page) {
            if (page >= 1) {
                currentPage = page;
                loadShipments();
            }
        }

        async function viewShipmentDetails(orderId) {
            try {
                const response = await fetch(`/admin/tracking/api/shipment/${orderId}`);
                const data = await response.json();
                
                // Create modal or redirect to details page
                alert(`Shipment Details for Order ${orderId}:\n\n${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                console.error('Error loading shipment details:', error);
                alert('Error loading shipment details');
            }
        }

        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>
</body>
</html>
