"""
Tracking Dashboard for Admin Monitoring
=======================================

Comprehensive admin dashboard for monitoring all shipments,
tracking events, and system performance in real-time.

Features:
1. Real-time shipment monitoring
2. Performance analytics and metrics
3. Exception handling and alerts
4. Carrier performance comparison
5. Interactive filtering and search
6. Export capabilities
7. System health monitoring
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
from dataclasses import dataclass, asdict
from collections import defaultdict

from flask import Blueprint, request, jsonify, render_template
from sqlalchemy import and_, or_, func, desc, asc
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

# ============================================================================
# DASHBOARD DATA STRUCTURES
# ============================================================================

@dataclass
class DashboardMetrics:
    """Dashboard metrics summary"""
    total_shipments: int
    active_shipments: int
    delivered_shipments: int
    delayed_shipments: int
    exception_shipments: int
    average_delivery_time: float
    on_time_delivery_rate: float
    carrier_performance: Dict[str, Dict[str, Any]]
    daily_shipment_volume: List[Dict[str, Any]]
    status_distribution: Dict[str, int]
    recent_exceptions: List[Dict[str, Any]]

@dataclass
class ShipmentSummary:
    """Individual shipment summary for dashboard"""
    order_id: int
    tracking_number: str
    carrier_code: str
    current_status: str
    created_date: datetime
    estimated_delivery: Optional[datetime]
    actual_delivery: Optional[datetime]
    days_in_transit: int
    is_delayed: bool
    exception_count: int
    last_location: Optional[str]
    customer_email: Optional[str]

class DashboardFilter(Enum):
    """Available dashboard filters"""
    ALL = "all"
    ACTIVE = "active"
    DELIVERED = "delivered"
    DELAYED = "delayed"
    EXCEPTIONS = "exceptions"
    CARRIER = "carrier"
    DATE_RANGE = "date_range"

# ============================================================================
# TRACKING DASHBOARD SERVICE
# ============================================================================

class TrackingDashboardService:
    """Service for tracking dashboard operations"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
    
    def get_dashboard_metrics(self, days: int = 30) -> DashboardMetrics:
        """Get comprehensive dashboard metrics"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # Get basic counts
            metrics = self._get_basic_metrics(start_date, end_date)
            
            # Get carrier performance
            carrier_performance = self._get_carrier_performance(start_date, end_date)
            
            # Get daily volume
            daily_volume = self._get_daily_shipment_volume(start_date, end_date)
            
            # Get status distribution
            status_distribution = self._get_status_distribution()
            
            # Get recent exceptions
            recent_exceptions = self._get_recent_exceptions(limit=10)
            
            return DashboardMetrics(
                total_shipments=metrics['total'],
                active_shipments=metrics['active'],
                delivered_shipments=metrics['delivered'],
                delayed_shipments=metrics['delayed'],
                exception_shipments=metrics['exceptions'],
                average_delivery_time=metrics['avg_delivery_time'],
                on_time_delivery_rate=metrics['on_time_rate'],
                carrier_performance=carrier_performance,
                daily_shipment_volume=daily_volume,
                status_distribution=status_distribution,
                recent_exceptions=recent_exceptions
            )
            
        except Exception as e:
            logger.error(f"Error getting dashboard metrics: {e}")
            return DashboardMetrics(
                total_shipments=0, active_shipments=0, delivered_shipments=0,
                delayed_shipments=0, exception_shipments=0, average_delivery_time=0.0,
                on_time_delivery_rate=0.0, carrier_performance={}, daily_shipment_volume=[],
                status_distribution={}, recent_exceptions=[]
            )
    
    def _get_basic_metrics(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get basic shipment metrics"""
        try:
            from app import Order, TrackingEvent
            from tracking_system import TrackingStatus
            
            # Total shipments in period
            total_query = self.db.query(func.count(Order.id)).filter(
                and_(Order.created_at >= start_date, Order.created_at <= end_date)
            )
            total_shipments = total_query.scalar() or 0
            
            # Active shipments (not delivered, cancelled, or returned)
            active_statuses = [
                TrackingStatus.LABEL_CREATED.value,
                TrackingStatus.PICKUP_SCHEDULED.value,
                TrackingStatus.PICKED_UP.value,
                TrackingStatus.IN_TRANSIT.value,
                TrackingStatus.OUT_FOR_DELIVERY.value,
                TrackingStatus.DELIVERY_ATTEMPTED.value
            ]
            
            active_query = self.db.query(func.count(Order.id)).join(TrackingEvent).filter(
                and_(
                    Order.created_at >= start_date,
                    Order.created_at <= end_date,
                    TrackingEvent.status.in_(active_statuses)
                )
            )
            active_shipments = active_query.scalar() or 0
            
            # Delivered shipments
            delivered_query = self.db.query(func.count(Order.id)).join(TrackingEvent).filter(
                and_(
                    Order.created_at >= start_date,
                    Order.created_at <= end_date,
                    TrackingEvent.status == TrackingStatus.DELIVERED.value
                )
            )
            delivered_shipments = delivered_query.scalar() or 0
            
            # Delayed shipments (estimated delivery passed)
            delayed_query = self.db.query(func.count(Order.id)).join(TrackingEvent).filter(
                and_(
                    Order.created_at >= start_date,
                    Order.created_at <= end_date,
                    TrackingEvent.estimated_delivery < datetime.now(),
                    TrackingEvent.status.in_(active_statuses)
                )
            )
            delayed_shipments = delayed_query.scalar() or 0
            
            # Exception shipments
            exception_statuses = [
                TrackingStatus.EXCEPTION.value,
                TrackingStatus.LOST.value,
                TrackingStatus.DAMAGED.value,
                TrackingStatus.RETURNED_TO_SENDER.value
            ]
            
            exception_query = self.db.query(func.count(Order.id)).join(TrackingEvent).filter(
                and_(
                    Order.created_at >= start_date,
                    Order.created_at <= end_date,
                    TrackingEvent.status.in_(exception_statuses)
                )
            )
            exception_shipments = exception_query.scalar() or 0
            
            # Average delivery time
            avg_delivery_time = self._calculate_average_delivery_time(start_date, end_date)
            
            # On-time delivery rate
            on_time_rate = self._calculate_on_time_delivery_rate(start_date, end_date)
            
            return {
                'total': total_shipments,
                'active': active_shipments,
                'delivered': delivered_shipments,
                'delayed': delayed_shipments,
                'exceptions': exception_shipments,
                'avg_delivery_time': avg_delivery_time,
                'on_time_rate': on_time_rate
            }
            
        except Exception as e:
            logger.error(f"Error getting basic metrics: {e}")
            return {
                'total': 0, 'active': 0, 'delivered': 0, 'delayed': 0,
                'exceptions': 0, 'avg_delivery_time': 0.0, 'on_time_rate': 0.0
            }
    
    def _get_carrier_performance(self, start_date: datetime, end_date: datetime) -> Dict[str, Dict[str, Any]]:
        """Get carrier performance metrics"""
        try:
            from app import TrackingEvent
            from tracking_system import TrackingStatus
            
            carriers = ['blue_dart', 'delhivery', 'fedex']
            performance = {}
            
            for carrier in carriers:
                # Total shipments for carrier
                total_query = self.db.query(func.count(TrackingEvent.id)).filter(
                    and_(
                        TrackingEvent.carrier_code == carrier,
                        TrackingEvent.timestamp >= start_date,
                        TrackingEvent.timestamp <= end_date
                    )
                )
                total = total_query.scalar() or 0
                
                # Delivered shipments
                delivered_query = self.db.query(func.count(TrackingEvent.id)).filter(
                    and_(
                        TrackingEvent.carrier_code == carrier,
                        TrackingEvent.status == TrackingStatus.DELIVERED.value,
                        TrackingEvent.timestamp >= start_date,
                        TrackingEvent.timestamp <= end_date
                    )
                )
                delivered = delivered_query.scalar() or 0
                
                # Exception shipments
                exception_statuses = [
                    TrackingStatus.EXCEPTION.value,
                    TrackingStatus.LOST.value,
                    TrackingStatus.DAMAGED.value
                ]
                
                exception_query = self.db.query(func.count(TrackingEvent.id)).filter(
                    and_(
                        TrackingEvent.carrier_code == carrier,
                        TrackingEvent.status.in_(exception_statuses),
                        TrackingEvent.timestamp >= start_date,
                        TrackingEvent.timestamp <= end_date
                    )
                )
                exceptions = exception_query.scalar() or 0
                
                # Calculate rates
                delivery_rate = (delivered / total * 100) if total > 0 else 0
                exception_rate = (exceptions / total * 100) if total > 0 else 0
                
                # Average delivery time for this carrier
                avg_delivery_time = self._calculate_carrier_delivery_time(carrier, start_date, end_date)
                
                performance[carrier] = {
                    'total_shipments': total,
                    'delivered_shipments': delivered,
                    'exception_shipments': exceptions,
                    'delivery_rate': round(delivery_rate, 2),
                    'exception_rate': round(exception_rate, 2),
                    'average_delivery_time': avg_delivery_time
                }
            
            return performance
            
        except Exception as e:
            logger.error(f"Error getting carrier performance: {e}")
            return {}
    
    def _get_daily_shipment_volume(self, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """Get daily shipment volume for charts"""
        try:
            from app import Order
            
            # Query daily shipment counts
            daily_counts = self.db.query(
                func.date(Order.created_at).label('date'),
                func.count(Order.id).label('count')
            ).filter(
                and_(Order.created_at >= start_date, Order.created_at <= end_date)
            ).group_by(func.date(Order.created_at)).order_by(func.date(Order.created_at)).all()
            
            # Convert to list of dictionaries
            volume_data = []
            for date, count in daily_counts:
                volume_data.append({
                    'date': date.strftime('%Y-%m-%d'),
                    'shipments': count
                })
            
            return volume_data
            
        except Exception as e:
            logger.error(f"Error getting daily volume: {e}")
            return []
    
    def _get_status_distribution(self) -> Dict[str, int]:
        """Get current status distribution of all shipments"""
        try:
            from app import TrackingEvent
            
            # Get latest status for each tracking number
            subquery = self.db.query(
                TrackingEvent.tracking_number,
                func.max(TrackingEvent.timestamp).label('latest_timestamp')
            ).group_by(TrackingEvent.tracking_number).subquery()
            
            # Get status distribution
            status_counts = self.db.query(
                TrackingEvent.status,
                func.count(TrackingEvent.tracking_number).label('count')
            ).join(
                subquery,
                and_(
                    TrackingEvent.tracking_number == subquery.c.tracking_number,
                    TrackingEvent.timestamp == subquery.c.latest_timestamp
                )
            ).group_by(TrackingEvent.status).all()
            
            distribution = {}
            for status, count in status_counts:
                distribution[status] = count
            
            return distribution
            
        except Exception as e:
            logger.error(f"Error getting status distribution: {e}")
            return {}
    
    def _get_recent_exceptions(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent exception events"""
        try:
            from app import TrackingEvent, Order
            from tracking_system import TrackingStatus
            
            exception_statuses = [
                TrackingStatus.EXCEPTION.value,
                TrackingStatus.LOST.value,
                TrackingStatus.DAMAGED.value,
                TrackingStatus.DELIVERY_ATTEMPTED.value
            ]
            
            exceptions = self.db.query(TrackingEvent, Order).join(
                Order, TrackingEvent.order_id == Order.id
            ).filter(
                TrackingEvent.status.in_(exception_statuses)
            ).order_by(desc(TrackingEvent.timestamp)).limit(limit).all()
            
            exception_list = []
            for event, order in exceptions:
                exception_list.append({
                    'order_id': order.id,
                    'tracking_number': event.tracking_number,
                    'status': event.status,
                    'description': event.description or event.exception_description,
                    'timestamp': event.timestamp.isoformat(),
                    'carrier': event.carrier_code,
                    'customer_email': order.email
                })
            
            return exception_list
            
        except Exception as e:
            logger.error(f"Error getting recent exceptions: {e}")
            return []
    
    def _calculate_average_delivery_time(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate average delivery time in days"""
        try:
            from app import Order, TrackingEvent
            from tracking_system import TrackingStatus
            
            # Get delivered orders with their delivery times
            delivered_orders = self.db.query(
                Order.created_at,
                TrackingEvent.timestamp.label('delivered_at')
            ).join(TrackingEvent).filter(
                and_(
                    Order.created_at >= start_date,
                    Order.created_at <= end_date,
                    TrackingEvent.status == TrackingStatus.DELIVERED.value
                )
            ).all()
            
            if not delivered_orders:
                return 0.0
            
            total_days = 0
            for order_date, delivery_date in delivered_orders:
                delivery_time = (delivery_date - order_date).total_seconds() / (24 * 3600)
                total_days += delivery_time
            
            return round(total_days / len(delivered_orders), 2)
            
        except Exception as e:
            logger.error(f"Error calculating average delivery time: {e}")
            return 0.0
    
    def _calculate_on_time_delivery_rate(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate on-time delivery rate"""
        try:
            from app import TrackingEvent
            from tracking_system import TrackingStatus
            
            # Get delivered shipments with estimated delivery dates
            delivered_with_estimates = self.db.query(TrackingEvent).filter(
                and_(
                    TrackingEvent.status == TrackingStatus.DELIVERED.value,
                    TrackingEvent.timestamp >= start_date,
                    TrackingEvent.timestamp <= end_date,
                    TrackingEvent.estimated_delivery.isnot(None)
                )
            ).all()
            
            if not delivered_with_estimates:
                return 0.0
            
            on_time_count = 0
            for event in delivered_with_estimates:
                if event.timestamp <= event.estimated_delivery:
                    on_time_count += 1
            
            return round((on_time_count / len(delivered_with_estimates)) * 100, 2)
            
        except Exception as e:
            logger.error(f"Error calculating on-time delivery rate: {e}")
            return 0.0
    
    def _calculate_carrier_delivery_time(self, carrier: str, start_date: datetime, end_date: datetime) -> float:
        """Calculate average delivery time for specific carrier"""
        try:
            from app import Order, TrackingEvent
            from tracking_system import TrackingStatus
            
            delivered_orders = self.db.query(
                Order.created_at,
                TrackingEvent.timestamp.label('delivered_at')
            ).join(TrackingEvent).filter(
                and_(
                    Order.created_at >= start_date,
                    Order.created_at <= end_date,
                    TrackingEvent.carrier_code == carrier,
                    TrackingEvent.status == TrackingStatus.DELIVERED.value
                )
            ).all()
            
            if not delivered_orders:
                return 0.0
            
            total_days = 0
            for order_date, delivery_date in delivered_orders:
                delivery_time = (delivery_date - order_date).total_seconds() / (24 * 3600)
                total_days += delivery_time
            
            return round(total_days / len(delivered_orders), 2)
            
        except Exception as e:
            logger.error(f"Error calculating carrier delivery time: {e}")
            return 0.0
    
    def get_shipment_list(self, filter_type: DashboardFilter = DashboardFilter.ALL, 
                         filter_value: str = None, page: int = 1, per_page: int = 50) -> Dict[str, Any]:
        """Get filtered list of shipments for dashboard table"""
        try:
            from app import Order, TrackingEvent
            from tracking_system import TrackingStatus
            
            # Base query
            query = self.db.query(Order, TrackingEvent).outerjoin(
                TrackingEvent, Order.id == TrackingEvent.order_id
            )
            
            # Apply filters
            if filter_type == DashboardFilter.ACTIVE:
                active_statuses = [
                    TrackingStatus.LABEL_CREATED.value,
                    TrackingStatus.PICKUP_SCHEDULED.value,
                    TrackingStatus.PICKED_UP.value,
                    TrackingStatus.IN_TRANSIT.value,
                    TrackingStatus.OUT_FOR_DELIVERY.value,
                    TrackingStatus.DELIVERY_ATTEMPTED.value
                ]
                query = query.filter(TrackingEvent.status.in_(active_statuses))
            
            elif filter_type == DashboardFilter.DELIVERED:
                query = query.filter(TrackingEvent.status == TrackingStatus.DELIVERED.value)
            
            elif filter_type == DashboardFilter.DELAYED:
                query = query.filter(
                    and_(
                        TrackingEvent.estimated_delivery < datetime.now(),
                        TrackingEvent.status != TrackingStatus.DELIVERED.value
                    )
                )
            
            elif filter_type == DashboardFilter.EXCEPTIONS:
                exception_statuses = [
                    TrackingStatus.EXCEPTION.value,
                    TrackingStatus.LOST.value,
                    TrackingStatus.DAMAGED.value,
                    TrackingStatus.RETURNED_TO_SENDER.value
                ]
                query = query.filter(TrackingEvent.status.in_(exception_statuses))
            
            elif filter_type == DashboardFilter.CARRIER and filter_value:
                query = query.filter(TrackingEvent.carrier_code == filter_value)
            
            # Get total count
            total_count = query.count()
            
            # Apply pagination
            offset = (page - 1) * per_page
            shipments = query.order_by(desc(Order.created_at)).offset(offset).limit(per_page).all()
            
            # Convert to shipment summaries
            shipment_list = []
            for order, tracking_event in shipments:
                if tracking_event:
                    days_in_transit = (datetime.now() - order.created_at).days
                    is_delayed = (tracking_event.estimated_delivery and 
                                datetime.now() > tracking_event.estimated_delivery and
                                tracking_event.status != TrackingStatus.DELIVERED.value)
                    
                    summary = ShipmentSummary(
                        order_id=order.id,
                        tracking_number=tracking_event.tracking_number,
                        carrier_code=tracking_event.carrier_code,
                        current_status=tracking_event.status,
                        created_date=order.created_at,
                        estimated_delivery=tracking_event.estimated_delivery,
                        actual_delivery=tracking_event.timestamp if tracking_event.status == TrackingStatus.DELIVERED.value else None,
                        days_in_transit=days_in_transit,
                        is_delayed=is_delayed,
                        exception_count=0,  # Would need separate query to count exceptions
                        last_location=tracking_event.location,
                        customer_email=order.email
                    )
                    
                    shipment_list.append(asdict(summary))
            
            return {
                'shipments': shipment_list,
                'total_count': total_count,
                'page': page,
                'per_page': per_page,
                'total_pages': (total_count + per_page - 1) // per_page
            }
            
        except Exception as e:
            logger.error(f"Error getting shipment list: {e}")
            return {'shipments': [], 'total_count': 0, 'page': 1, 'per_page': per_page, 'total_pages': 0}

# ============================================================================
# FLASK BLUEPRINT FOR DASHBOARD
# ============================================================================

dashboard_bp = Blueprint('tracking_dashboard', __name__, url_prefix='/admin/tracking')

@dashboard_bp.route('/dashboard')
def dashboard_view():
    """Render tracking dashboard page"""
    return render_template('admin/tracking_dashboard.html')

@dashboard_bp.route('/api/metrics')
def get_metrics():
    """Get dashboard metrics API"""
    try:
        days = request.args.get('days', 30, type=int)
        
        from app import db
        dashboard_service = TrackingDashboardService(db.session)
        metrics = dashboard_service.get_dashboard_metrics(days)
        
        return jsonify(asdict(metrics))
        
    except Exception as e:
        logger.error(f"Error getting dashboard metrics: {e}")
        return jsonify({'error': 'Failed to get metrics'}), 500

@dashboard_bp.route('/api/shipments')
def get_shipments():
    """Get shipments list API"""
    try:
        filter_type = request.args.get('filter', 'all')
        filter_value = request.args.get('filter_value')
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        
        try:
            filter_enum = DashboardFilter(filter_type)
        except ValueError:
            filter_enum = DashboardFilter.ALL
        
        from app import db
        dashboard_service = TrackingDashboardService(db.session)
        result = dashboard_service.get_shipment_list(filter_enum, filter_value, page, per_page)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error getting shipments: {e}")
        return jsonify({'error': 'Failed to get shipments'}), 500

@dashboard_bp.route('/api/shipment/<int:order_id>')
def get_shipment_details(order_id):
    """Get detailed shipment information"""
    try:
        from app import db, Order, TrackingEvent
        
        # Get order details
        order = db.session.query(Order).filter(Order.id == order_id).first()
        if not order:
            return jsonify({'error': 'Order not found'}), 404
        
        # Get all tracking events for this order
        events = db.session.query(TrackingEvent).filter(
            TrackingEvent.order_id == order_id
        ).order_by(TrackingEvent.timestamp).all()
        
        # Format response
        shipment_details = {
            'order': {
                'id': order.id,
                'created_at': order.created_at.isoformat(),
                'customer_email': order.email,
                'total_amount': float(order.total_amount),
                'status': order.status
            },
            'tracking_events': [
                {
                    'tracking_number': event.tracking_number,
                    'status': event.status,
                    'event_type': event.event_type,
                    'timestamp': event.timestamp.isoformat(),
                    'location': event.location,
                    'description': event.description,
                    'carrier_code': event.carrier_code,
                    'carrier_status': event.carrier_status,
                    'estimated_delivery': event.estimated_delivery.isoformat() if event.estimated_delivery else None,
                    'exception_code': event.exception_code,
                    'exception_description': event.exception_description
                }
                for event in events
            ]
        }
        
        return jsonify(shipment_details)
        
    except Exception as e:
        logger.error(f"Error getting shipment details: {e}")
        return jsonify({'error': 'Failed to get shipment details'}), 500
