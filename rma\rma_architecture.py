"""
Return/Exchange Management Architecture (RMA System)
===================================================

Comprehensive RMA system architecture for handling returns, exchanges, and refunds
with full workflow automation, approval processes, and integration with existing
order fulfillment and inventory management systems.

Key Components:
1. RMA Request Management
2. Return/Exchange Processing Engine
3. Approval Workflow System
4. Inventory Integration
5. Refund Processing
6. Return Shipping Management
7. Customer Communication
8. Admin Dashboard Integration

Database Models:
- RMARequest
- RMAItem
- ReturnShipment
- ExchangeRequest
- RMAApproval
- RMADocument
- ReturnReason
- RMATimeline

Integration Points:
- Existing Order/OrderItem models
- Refund model (enhanced)
- Inventory Management
- Shipping/Fulfillment System
- Notification Service
- Payment Gateway
"""

from enum import Enum
from dataclasses import dataclass
from typing import List, Dict, Optional, Any, Union
from datetime import datetime, date, timedelta
import json

# RMA Status Enums
class RMAStatus(Enum):
    """RMA request status"""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    RETURN_SHIPPED = "return_shipped"
    RETURN_RECEIVED = "return_received"
    INSPECTING = "inspecting"
    INSPECTION_PASSED = "inspection_passed"
    INSPECTION_FAILED = "inspection_failed"
    REFUND_PROCESSING = "refund_processing"
    REFUND_COMPLETED = "refund_completed"
    EXCHANGE_PROCESSING = "exchange_processing"
    EXCHANGE_SHIPPED = "exchange_shipped"
    EXCHANGE_COMPLETED = "exchange_completed"
    CANCELLED = "cancelled"
    CLOSED = "closed"

class RMAType(Enum):
    """Type of RMA request"""
    RETURN_REFUND = "return_refund"
    EXCHANGE_SAME = "exchange_same"
    EXCHANGE_DIFFERENT = "exchange_different"
    REPAIR = "repair"
    WARRANTY_CLAIM = "warranty_claim"

class ReturnReason(Enum):
    """Reason for return/exchange"""
    DEFECTIVE = "defective"
    WRONG_ITEM = "wrong_item"
    NOT_AS_DESCRIBED = "not_as_described"
    SIZE_ISSUE = "size_issue"
    COLOR_ISSUE = "color_issue"
    DAMAGED_SHIPPING = "damaged_shipping"
    CHANGED_MIND = "changed_mind"
    BETTER_PRICE_FOUND = "better_price_found"
    DUPLICATE_ORDER = "duplicate_order"
    QUALITY_ISSUE = "quality_issue"
    MISSING_PARTS = "missing_parts"
    EXPIRED_PRODUCT = "expired_product"
    OTHER = "other"

class InspectionResult(Enum):
    """Result of return inspection"""
    PASSED = "passed"
    FAILED = "failed"
    PARTIAL = "partial"
    PENDING = "pending"

class RefundMethod(Enum):
    """Method for processing refund"""
    ORIGINAL_PAYMENT = "original_payment"
    STORE_CREDIT = "store_credit"
    BANK_TRANSFER = "bank_transfer"
    CHECK = "check"

# Data Classes
@dataclass
class RMAConfiguration:
    """RMA system configuration"""
    return_window_days: int = 30
    exchange_window_days: int = 30
    auto_approve_threshold: float = 500.0  # Auto-approve returns under this amount
    require_photos: bool = True
    require_original_packaging: bool = False
    restocking_fee_percentage: float = 0.0
    free_return_shipping: bool = True
    inspection_required: bool = True
    max_return_attempts: int = 3
    
    # Return reasons that require approval
    approval_required_reasons: List[str] = None
    
    # Return reasons that are auto-rejected
    auto_reject_reasons: List[str] = None
    
    def __post_init__(self):
        if self.approval_required_reasons is None:
            self.approval_required_reasons = [
                ReturnReason.CHANGED_MIND.value,
                ReturnReason.BETTER_PRICE_FOUND.value
            ]
        
        if self.auto_reject_reasons is None:
            self.auto_reject_reasons = []

@dataclass
class RMAItem:
    """Item being returned/exchanged"""
    order_item_id: int
    product_id: int
    quantity: int
    unit_price: float
    return_reason: ReturnReason
    condition_notes: Optional[str] = None
    photos: List[str] = None
    
    # For exchanges
    exchange_product_id: Optional[int] = None
    exchange_variant_id: Optional[int] = None
    exchange_quantity: Optional[int] = None
    
    def __post_init__(self):
        if self.photos is None:
            self.photos = []

@dataclass
class RMARequest:
    """Complete RMA request"""
    rma_number: str
    order_id: int
    user_id: Optional[int]
    rma_type: RMAType
    status: RMAStatus
    items: List[RMAItem]

    # Customer information
    customer_email: str

    # Timestamps (required fields - must come before optional fields)
    created_at: datetime
    updated_at: datetime
    customer_phone: Optional[str] = None
    customer_notes: Optional[str] = None
    
    # Processing information
    total_refund_amount: float = 0.0
    restocking_fee: float = 0.0
    return_shipping_cost: float = 0.0
    
    # Approval workflow
    requires_approval: bool = True
    approved_by: Optional[str] = None
    approved_at: Optional[datetime] = None
    rejection_reason: Optional[str] = None
    
    # Return shipping
    return_label_url: Optional[str] = None
    return_tracking_number: Optional[str] = None
    return_carrier: Optional[str] = None
    
    # Inspection
    inspection_result: Optional[InspectionResult] = None
    inspection_notes: Optional[str] = None
    inspected_by: Optional[str] = None
    inspected_at: Optional[datetime] = None
    
    # Refund processing
    refund_method: Optional[RefundMethod] = None
    refund_reference: Optional[str] = None
    refunded_at: Optional[datetime] = None
    
    # Optional timestamp
    deadline: Optional[datetime] = None
    
    def calculate_refund_amount(self) -> float:
        """Calculate total refund amount"""
        item_total = sum(item.quantity * item.unit_price for item in self.items)
        return max(0, item_total - self.restocking_fee - self.return_shipping_cost)
    
    def is_eligible(self) -> bool:
        """Check if RMA is still eligible based on time limits"""
        if not self.deadline:
            return True
        return datetime.utcnow() <= self.deadline
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'rma_number': self.rma_number,
            'order_id': self.order_id,
            'user_id': self.user_id,
            'rma_type': self.rma_type.value,
            'status': self.status.value,
            'items': [item.__dict__ for item in self.items],
            'customer_email': self.customer_email,
            'customer_phone': self.customer_phone,
            'customer_notes': self.customer_notes,
            'total_refund_amount': self.total_refund_amount,
            'restocking_fee': self.restocking_fee,
            'return_shipping_cost': self.return_shipping_cost,
            'requires_approval': self.requires_approval,
            'approved_by': self.approved_by,
            'approved_at': self.approved_at.isoformat() if self.approved_at else None,
            'rejection_reason': self.rejection_reason,
            'return_label_url': self.return_label_url,
            'return_tracking_number': self.return_tracking_number,
            'return_carrier': self.return_carrier,
            'inspection_result': self.inspection_result.value if self.inspection_result else None,
            'inspection_notes': self.inspection_notes,
            'inspected_by': self.inspected_by,
            'inspected_at': self.inspected_at.isoformat() if self.inspected_at else None,
            'refund_method': self.refund_method.value if self.refund_method else None,
            'refund_reference': self.refund_reference,
            'refunded_at': self.refunded_at.isoformat() if self.refunded_at else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'deadline': self.deadline.isoformat() if self.deadline else None
        }

@dataclass
class RMAWorkflowRule:
    """Business rule for RMA processing"""
    name: str
    condition: str  # Python expression to evaluate
    action: str     # Action to take if condition is true
    priority: int = 0
    active: bool = True

    def evaluate(self, rma_request: RMARequest) -> bool:
        """Evaluate if this rule applies to the RMA request"""
        try:
            # Create evaluation context
            context = {
                'rma': rma_request,
                'total_amount': rma_request.total_refund_amount,
                'item_count': len(rma_request.items),
                'rma_type': rma_request.rma_type.value,
                'return_reasons': [item.return_reason.value for item in rma_request.items]
            }

            # Evaluate condition
            return eval(self.condition, {"__builtins__": {}}, context)
        except Exception as e:
            print(f"Error evaluating RMA rule {self.name}: {e}")
            return False

    def evaluate_context(self, context: Dict[str, Any]) -> bool:
        """Evaluate rule with provided context"""
        try:
            return eval(self.condition, {"__builtins__": {}}, context)
        except Exception as e:
            print(f"Error evaluating RMA rule {self.name}: {e}")
            return False

# Default RMA Workflow Rules
DEFAULT_RMA_RULES = [
    RMAWorkflowRule(
        name="Auto Approve Low Value",
        condition="total_amount < 500",
        action="auto_approve",
        priority=1
    ),
    RMAWorkflowRule(
        name="Require Approval High Value",
        condition="total_amount >= 2000",
        action="require_approval",
        priority=2
    ),
    RMAWorkflowRule(
        name="Auto Reject Changed Mind High Value",
        condition="total_amount > 1000 and 'changed_mind' in return_reasons",
        action="auto_reject",
        priority=3
    ),
    RMAWorkflowRule(
        name="Require Photos Defective",
        condition="'defective' in return_reasons or 'damaged_shipping' in return_reasons",
        action="require_photos",
        priority=4
    ),
    RMAWorkflowRule(
        name="Skip Inspection Store Credit",
        condition="rma_type == 'return_refund' and total_amount < 200",
        action="skip_inspection",
        priority=5
    )
]

# RMA System Configuration
RMA_CONFIG = RMAConfiguration(
    return_window_days=30,
    exchange_window_days=30,
    auto_approve_threshold=500.0,
    require_photos=True,
    require_original_packaging=False,
    restocking_fee_percentage=0.0,
    free_return_shipping=True,
    inspection_required=True,
    max_return_attempts=3
)
