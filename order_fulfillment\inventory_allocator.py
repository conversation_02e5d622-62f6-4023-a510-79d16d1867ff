"""
Inventory Allocation System
==========================

Advanced inventory allocation and management system for order fulfillment.
Handles inventory reservation, allocation, release, and multi-channel synchronization.

Features:
1. Real-time inventory allocation
2. Reservation system with timeouts
3. Multi-channel inventory synchronization
4. Allocation conflict resolution
5. Inventory tracking and audit
6. Performance optimization
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from enum import Enum
from dataclasses import dataclass
import threading
from contextlib import contextmanager

from sqlalchemy import and_, or_, func
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

logger = logging.getLogger(__name__)

# ============================================================================
# ENUMS AND DATA STRUCTURES
# ============================================================================

class AllocationStatus(Enum):
    """Inventory allocation status"""
    PENDING = "pending"
    RESERVED = "reserved"
    ALLOCATED = "allocated"
    RELEASED = "released"
    EXPIRED = "expired"
    FAILED = "failed"

class InventoryChannel(Enum):
    """Inventory channels"""
    WEBSITE = "website"
    MOBILE_APP = "mobile_app"
    MARKETPLACE = "marketplace"
    RETAIL_STORE = "retail_store"
    WHOLESALE = "wholesale"

@dataclass
class AllocationRequest:
    """Inventory allocation request"""
    product_id: int
    quantity: int
    order_id: int
    channel: InventoryChannel
    priority: int = 1  # 1 = highest priority
    reservation_timeout: int = 300  # 5 minutes default
    
@dataclass
class AllocationResult:
    """Result of inventory allocation"""
    success: bool
    product_id: int
    requested_quantity: int
    allocated_quantity: int
    remaining_quantity: int
    reservation_id: Optional[str]
    error_message: Optional[str]
    timestamp: datetime

@dataclass
class InventoryReservation:
    """Inventory reservation record"""
    reservation_id: str
    product_id: int
    quantity: int
    order_id: int
    channel: InventoryChannel
    status: AllocationStatus
    created_at: datetime
    expires_at: datetime
    allocated_at: Optional[datetime] = None
    released_at: Optional[datetime] = None

# ============================================================================
# ADVANCED INVENTORY ALLOCATOR
# ============================================================================

class AdvancedInventoryAllocator:
    """Advanced inventory allocation system with reservation and multi-channel support"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.reservations: Dict[str, InventoryReservation] = {}
        self.allocation_lock = threading.RLock()
        
        # Start cleanup thread for expired reservations
        self._start_cleanup_thread()
    
    def _start_cleanup_thread(self):
        """Start background thread to clean up expired reservations"""
        def cleanup_expired_reservations():
            while True:
                try:
                    self._cleanup_expired_reservations()
                    threading.Event().wait(60)  # Check every minute
                except Exception as e:
                    logger.error(f"Error in reservation cleanup: {e}")
        
        cleanup_thread = threading.Thread(target=cleanup_expired_reservations, daemon=True)
        cleanup_thread.start()
    
    def _cleanup_expired_reservations(self):
        """Clean up expired reservations"""
        now = datetime.now()
        expired_reservations = []
        
        with self.allocation_lock:
            for reservation_id, reservation in self.reservations.items():
                if reservation.expires_at <= now and reservation.status == AllocationStatus.RESERVED:
                    expired_reservations.append(reservation_id)
            
            # Release expired reservations
            for reservation_id in expired_reservations:
                reservation = self.reservations[reservation_id]
                self._release_reservation_internal(reservation)
                reservation.status = AllocationStatus.EXPIRED
                logger.info(f"Expired reservation {reservation_id} for product {reservation.product_id}")
    
    @contextmanager
    def _inventory_lock(self, product_id: int):
        """Context manager for product-specific inventory locking"""
        with self.allocation_lock:
            yield
    
    def reserve_inventory(self, requests: List[AllocationRequest]) -> List[AllocationResult]:
        """Reserve inventory for multiple products"""
        results = []
        successful_reservations = []
        
        try:
            # Sort requests by priority
            sorted_requests = sorted(requests, key=lambda r: r.priority)
            
            for request in sorted_requests:
                result = self._reserve_single_product(request)
                results.append(result)
                
                if result.success:
                    successful_reservations.append(result.reservation_id)
                else:
                    # If any reservation fails, release all successful ones
                    for reservation_id in successful_reservations:
                        self.release_reservation(reservation_id)
                    break
            
            return results
            
        except Exception as e:
            logger.error(f"Error reserving inventory: {e}")
            # Release any successful reservations
            for reservation_id in successful_reservations:
                self.release_reservation(reservation_id)
            return [AllocationResult(
                success=False,
                product_id=req.product_id,
                requested_quantity=req.quantity,
                allocated_quantity=0,
                remaining_quantity=0,
                reservation_id=None,
                error_message=str(e),
                timestamp=datetime.now()
            ) for req in requests]
    
    def _reserve_single_product(self, request: AllocationRequest) -> AllocationResult:
        """Reserve inventory for a single product"""
        try:
            from app import Product
            
            with self._inventory_lock(request.product_id):
                # Get product with lock
                product = self.db.query(Product).filter(
                    Product.id == request.product_id
                ).with_for_update().first()
                
                if not product:
                    return AllocationResult(
                        success=False,
                        product_id=request.product_id,
                        requested_quantity=request.quantity,
                        allocated_quantity=0,
                        remaining_quantity=0,
                        reservation_id=None,
                        error_message="Product not found",
                        timestamp=datetime.now()
                    )
                
                # Calculate available quantity (considering existing reservations)
                reserved_quantity = self._get_reserved_quantity(request.product_id)
                available_quantity = product.stock_quantity - reserved_quantity
                
                if available_quantity < request.quantity:
                    return AllocationResult(
                        success=False,
                        product_id=request.product_id,
                        requested_quantity=request.quantity,
                        allocated_quantity=0,
                        remaining_quantity=available_quantity,
                        reservation_id=None,
                        error_message=f"Insufficient inventory. Available: {available_quantity}, Requested: {request.quantity}",
                        timestamp=datetime.now()
                    )
                
                # Create reservation
                reservation_id = f"RES-{datetime.now().strftime('%Y%m%d%H%M%S')}-{request.product_id}-{request.order_id}"
                reservation = InventoryReservation(
                    reservation_id=reservation_id,
                    product_id=request.product_id,
                    quantity=request.quantity,
                    order_id=request.order_id,
                    channel=request.channel,
                    status=AllocationStatus.RESERVED,
                    created_at=datetime.now(),
                    expires_at=datetime.now() + timedelta(seconds=request.reservation_timeout)
                )
                
                self.reservations[reservation_id] = reservation
                
                logger.info(f"Reserved {request.quantity} units of product {request.product_id} for order {request.order_id}")
                
                return AllocationResult(
                    success=True,
                    product_id=request.product_id,
                    requested_quantity=request.quantity,
                    allocated_quantity=request.quantity,
                    remaining_quantity=available_quantity - request.quantity,
                    reservation_id=reservation_id,
                    error_message=None,
                    timestamp=datetime.now()
                )
                
        except Exception as e:
            logger.error(f"Error reserving product {request.product_id}: {e}")
            return AllocationResult(
                success=False,
                product_id=request.product_id,
                requested_quantity=request.quantity,
                allocated_quantity=0,
                remaining_quantity=0,
                reservation_id=None,
                error_message=str(e),
                timestamp=datetime.now()
            )
    
    def _get_reserved_quantity(self, product_id: int) -> int:
        """Get total reserved quantity for a product"""
        total_reserved = 0
        for reservation in self.reservations.values():
            if (reservation.product_id == product_id and 
                reservation.status == AllocationStatus.RESERVED):
                total_reserved += reservation.quantity
        return total_reserved
    
    def allocate_reserved_inventory(self, reservation_ids: List[str]) -> List[AllocationResult]:
        """Convert reservations to actual allocations"""
        results = []
        
        try:
            from app import Product
            
            for reservation_id in reservation_ids:
                if reservation_id not in self.reservations:
                    results.append(AllocationResult(
                        success=False,
                        product_id=0,
                        requested_quantity=0,
                        allocated_quantity=0,
                        remaining_quantity=0,
                        reservation_id=reservation_id,
                        error_message="Reservation not found",
                        timestamp=datetime.now()
                    ))
                    continue
                
                reservation = self.reservations[reservation_id]
                
                # Check if reservation is still valid
                if reservation.status != AllocationStatus.RESERVED:
                    results.append(AllocationResult(
                        success=False,
                        product_id=reservation.product_id,
                        requested_quantity=reservation.quantity,
                        allocated_quantity=0,
                        remaining_quantity=0,
                        reservation_id=reservation_id,
                        error_message=f"Reservation status is {reservation.status.value}",
                        timestamp=datetime.now()
                    ))
                    continue
                
                if datetime.now() > reservation.expires_at:
                    reservation.status = AllocationStatus.EXPIRED
                    results.append(AllocationResult(
                        success=False,
                        product_id=reservation.product_id,
                        requested_quantity=reservation.quantity,
                        allocated_quantity=0,
                        remaining_quantity=0,
                        reservation_id=reservation_id,
                        error_message="Reservation expired",
                        timestamp=datetime.now()
                    ))
                    continue
                
                # Allocate inventory
                with self._inventory_lock(reservation.product_id):
                    product = self.db.query(Product).filter(
                        Product.id == reservation.product_id
                    ).with_for_update().first()
                    
                    if not product:
                        results.append(AllocationResult(
                            success=False,
                            product_id=reservation.product_id,
                            requested_quantity=reservation.quantity,
                            allocated_quantity=0,
                            remaining_quantity=0,
                            reservation_id=reservation_id,
                            error_message="Product not found",
                            timestamp=datetime.now()
                        ))
                        continue
                    
                    if product.stock_quantity < reservation.quantity:
                        results.append(AllocationResult(
                            success=False,
                            product_id=reservation.product_id,
                            requested_quantity=reservation.quantity,
                            allocated_quantity=0,
                            remaining_quantity=product.stock_quantity,
                            reservation_id=reservation_id,
                            error_message="Insufficient stock for allocation",
                            timestamp=datetime.now()
                        ))
                        continue
                    
                    # Reduce stock
                    product.stock_quantity -= reservation.quantity
                    
                    # Update reservation status
                    reservation.status = AllocationStatus.ALLOCATED
                    reservation.allocated_at = datetime.now()
                    
                    results.append(AllocationResult(
                        success=True,
                        product_id=reservation.product_id,
                        requested_quantity=reservation.quantity,
                        allocated_quantity=reservation.quantity,
                        remaining_quantity=product.stock_quantity,
                        reservation_id=reservation_id,
                        error_message=None,
                        timestamp=datetime.now()
                    ))
                    
                    logger.info(f"Allocated {reservation.quantity} units of product {reservation.product_id}")
            
            # Commit all changes
            self.db.commit()
            return results
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error allocating reserved inventory: {e}")
            return [AllocationResult(
                success=False,
                product_id=0,
                requested_quantity=0,
                allocated_quantity=0,
                remaining_quantity=0,
                reservation_id=rid,
                error_message=str(e),
                timestamp=datetime.now()
            ) for rid in reservation_ids]
    
    def release_reservation(self, reservation_id: str) -> bool:
        """Release a reservation"""
        try:
            if reservation_id not in self.reservations:
                logger.warning(f"Reservation {reservation_id} not found")
                return False
            
            reservation = self.reservations[reservation_id]
            return self._release_reservation_internal(reservation)
            
        except Exception as e:
            logger.error(f"Error releasing reservation {reservation_id}: {e}")
            return False
    
    def _release_reservation_internal(self, reservation: InventoryReservation) -> bool:
        """Internal method to release a reservation"""
        try:
            if reservation.status == AllocationStatus.ALLOCATED:
                # If already allocated, return stock to inventory
                from app import Product
                
                with self._inventory_lock(reservation.product_id):
                    product = self.db.query(Product).filter(
                        Product.id == reservation.product_id
                    ).with_for_update().first()
                    
                    if product:
                        product.stock_quantity += reservation.quantity
                        self.db.commit()
                        logger.info(f"Returned {reservation.quantity} units to product {reservation.product_id}")
            
            reservation.status = AllocationStatus.RELEASED
            reservation.released_at = datetime.now()
            
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error in internal release: {e}")
            return False
    
    def get_reservation_status(self, reservation_id: str) -> Optional[AllocationStatus]:
        """Get status of a reservation"""
        if reservation_id in self.reservations:
            return self.reservations[reservation_id].status
        return None
    
    def get_product_availability(self, product_id: int) -> Dict[str, Any]:
        """Get detailed availability information for a product"""
        try:
            from app import Product
            
            product = self.db.query(Product).filter(Product.id == product_id).first()
            if not product:
                return {'error': 'Product not found'}
            
            reserved_quantity = self._get_reserved_quantity(product_id)
            available_quantity = product.stock_quantity - reserved_quantity
            
            return {
                'product_id': product_id,
                'total_stock': product.stock_quantity,
                'reserved_quantity': reserved_quantity,
                'available_quantity': available_quantity,
                'reservations': [
                    {
                        'reservation_id': r.reservation_id,
                        'quantity': r.quantity,
                        'order_id': r.order_id,
                        'status': r.status.value,
                        'expires_at': r.expires_at.isoformat()
                    }
                    for r in self.reservations.values()
                    if r.product_id == product_id and r.status == AllocationStatus.RESERVED
                ]
            }
            
        except Exception as e:
            logger.error(f"Error getting product availability: {e}")
            return {'error': str(e)}

# ============================================================================
# INTEGRATION FUNCTIONS
# ============================================================================

def create_inventory_allocator(db_session: Session) -> AdvancedInventoryAllocator:
    """Create a new inventory allocator instance"""
    return AdvancedInventoryAllocator(db_session)

# Global allocator instance (can be used as singleton)
_global_allocator = None
_allocator_lock = threading.Lock()

def get_global_allocator(db_session: Session) -> AdvancedInventoryAllocator:
    """Get global allocator instance"""
    global _global_allocator
    
    if _global_allocator is None:
        with _allocator_lock:
            if _global_allocator is None:
                _global_allocator = AdvancedInventoryAllocator(db_session)
    
    return _global_allocator
