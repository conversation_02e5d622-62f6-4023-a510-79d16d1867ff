"""
Visual Search Model Package
===========================

This package contains the visual search system components:
- visual_search_model.py: Core visual search model for feature extraction and similarity computation
- visual_search_model.pkl: Trained visual features data for all products

The visual search system provides:
- Multi-modal feature extraction (CNN, color, texture, shape)
- Advanced similarity computation
- Visual product search capabilities
- Integration with e-commerce product catalog

Author: Allora Development Team
Date: 2025-07-12
"""

from .visual_search_model import SeededDataVisualSearchModel

__all__ = [
    'SeededDataVisualSearchModel'
]

__version__ = "1.0.0"
__author__ = "Allora Development Team"
