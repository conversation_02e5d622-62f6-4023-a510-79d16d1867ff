"""
Order Fulfillment System Package
================================

Comprehensive order fulfillment system with multi-carrier integration,
intelligent business rules, inventory management, and real-time tracking.

Components:
- Order Fulfillment Engine: Core orchestration and workflow management
- Carrier Integration: Multi-carrier shipping API integration
- Fulfillment API: RESTful endpoints for fulfillment operations
- Business Rules: Configurable fulfillment logic and automation
- Inventory Allocator: Advanced inventory management with reservations
- Architecture: Core data structures and enums

Author: Allora Development Team
Date: 2025-07-13
"""

# Import main components for easy access
from .order_fulfillment_engine import (
    OrderFulfillmentEngine,
    FulfillmentStatus,
    FulfillmentPriority,
    FulfillmentResult,
    create_fulfillment_engine,
    process_order_fulfillment,
    integrate_with_checkout
)

from .carrier_integration import (
    CarrierFactory,
    RateCalculationEngine,
    ShipmentManager,
    ShippingCarrier,
    CarrierAPIError,
    BaseCarrierAPI
)

from .fulfillment_api import fulfillment_bp

from .order_fulfillment_architecture import (
    Address,
    Package,
    FulfillmentRequest,
    TrackingEventType,
    ShipmentStatus,
    PackageType,
    ShippingRate,
    TrackingEvent,
    FulfillmentConfig
)

from .inventory_allocator import (
    AdvancedInventoryAllocator,
    AllocationRequest,
    AllocationResult,
    InventoryReservation,
    InventoryChannel
)

from .fulfillment_rules import (
    FulfillmentRuleManager,
    DefaultFulfillmentRules,
    BusinessRuleType,
    FulfillmentRuleConfig,
    RuleCondition,
    RuleConditionOperator
)

__all__ = [
    # Order Fulfillment Engine
    'OrderFulfillmentEngine',
    'FulfillmentStatus',
    'FulfillmentPriority',
    'FulfillmentResult',
    'create_fulfillment_engine',
    'process_order_fulfillment',
    'integrate_with_checkout',
    
    # Carrier Integration
    'CarrierFactory',
    'RateCalculationEngine',
    'ShipmentManager',
    'ShippingCarrier',
    'CarrierAPIError',
    'BaseCarrierAPI',
    
    # Fulfillment API
    'fulfillment_bp',
    
    # Architecture
    'Address',
    'Package',
    'FulfillmentRequest',
    'TrackingEventType',
    'ShipmentStatus',
    'PackageType',
    'ShippingRate',
    'TrackingEvent',
    'FulfillmentConfig',
    
    # Inventory Allocator
    'AdvancedInventoryAllocator',
    'AllocationRequest',
    'AllocationResult',
    'InventoryReservation',
    'InventoryChannel',
    
    # Business Rules
    'FulfillmentRuleManager',
    'DefaultFulfillmentRules',
    'BusinessRuleType',
    'FulfillmentRuleConfig',
    'RuleCondition',
    'RuleConditionOperator'
]

# Package metadata
__version__ = '1.0.0'
__author__ = 'Allora Development Team'
__description__ = 'Comprehensive order fulfillment system with multi-carrier integration'
