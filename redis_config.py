"""
Redis Configuration and Connection Management for Allora E-commerce
================================================================

This module handles Redis connections, caching, and session management
with proper fallback mechanisms when Redis is not available.

Author: Allora Development Team
Date: 2025-07-07
"""

import logging
import redis
from typing import Optional, Any, Dict
import json
import pickle
from datetime import datetime, timedelta
from config import get_config

logger = logging.getLogger(__name__)

class RedisConfig:
    """Redis configuration and connection management"""
    
    def __init__(self, config=None):
        """Initialize Redis configuration"""
        if config:
            self.host = getattr(config, 'REDIS_HOST', 'localhost')
            self.port = getattr(config, 'REDIS_PORT', 6379)
            self.db = getattr(config, 'REDIS_DB', 0)
            self.password = getattr(config, 'REDIS_PASSWORD', None)
            self.url = getattr(config, 'REDIS_URL', None)
        else:
            # Use centralized configuration
            config_class = get_config()()
            self.host = getattr(config_class, 'REDIS_HOST', 'localhost')
            self.port = getattr(config_class, 'REDIS_PORT', 6379)
            self.db = getattr(config_class, 'REDIS_DB', 0)
            self.password = getattr(config_class, 'REDIS_PASSWORD', None)
            self.url = getattr(config_class, 'REDIS_URL', None)
        
        self._client = None
        self._available = None
    
    def get_client(self) -> Optional[redis.Redis]:
        """Get Redis client with connection pooling"""
        if self._client is None:
            try:
                if self.url:
                    self._client = redis.from_url(
                        self.url,
                        decode_responses=True,
                        socket_connect_timeout=5,
                        socket_timeout=5,
                        retry_on_timeout=True,
                        health_check_interval=30
                    )
                else:
                    self._client = redis.Redis(
                        host=self.host,
                        port=self.port,
                        db=self.db,
                        password=self.password,
                        decode_responses=True,
                        socket_connect_timeout=5,
                        socket_timeout=5,
                        retry_on_timeout=True,
                        health_check_interval=30
                    )
                
                # Test connection
                self._client.ping()
                logger.info(f"✅ Connected to Redis at {self.host}:{self.port}")
                
            except Exception as e:
                logger.warning(f"⚠️  Redis connection failed: {e}")
                self._client = None
        
        return self._client
    
    def is_available(self) -> bool:
        """Check if Redis is available"""
        if self._available is None:
            try:
                client = self.get_client()
                if client:
                    client.ping()
                    self._available = True
                else:
                    self._available = False
            except Exception:
                self._available = False
        
        return self._available
    
    def reset_connection(self):
        """Reset Redis connection (useful for reconnection)"""
        self._client = None
        self._available = None

class RedisCache:
    """Redis-based caching with fallback to in-memory cache"""
    
    def __init__(self, redis_config: RedisConfig):
        self.redis_config = redis_config
        self._fallback_cache = {}  # In-memory fallback
        self._fallback_expiry = {}  # Track expiry times
    
    def _cleanup_fallback(self):
        """Clean up expired items from fallback cache"""
        now = datetime.utcnow()
        expired_keys = [
            key for key, expiry in self._fallback_expiry.items()
            if expiry and expiry < now
        ]
        for key in expired_keys:
            self._fallback_cache.pop(key, None)
            self._fallback_expiry.pop(key, None)
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        try:
            client = self.redis_config.get_client()
            if client:
                value = client.get(key)
                if value:
                    try:
                        return json.loads(value)
                    except json.JSONDecodeError:
                        return value
                return None
        except Exception as e:
            logger.debug(f"Redis get error: {e}")
        
        # Fallback to in-memory cache
        self._cleanup_fallback()
        return self._fallback_cache.get(key)
    
    def set(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """Set value in cache"""
        try:
            client = self.redis_config.get_client()
            if client:
                if isinstance(value, (dict, list)):
                    value = json.dumps(value)
                
                if expire:
                    return client.setex(key, expire, value)
                else:
                    return client.set(key, value)
        except Exception as e:
            logger.debug(f"Redis set error: {e}")
        
        # Fallback to in-memory cache
        self._fallback_cache[key] = value
        if expire:
            self._fallback_expiry[key] = datetime.utcnow() + timedelta(seconds=expire)
        else:
            self._fallback_expiry[key] = None
        return True
    
    def delete(self, key: str) -> bool:
        """Delete key from cache"""
        try:
            client = self.redis_config.get_client()
            if client:
                return bool(client.delete(key))
        except Exception as e:
            logger.debug(f"Redis delete error: {e}")
        
        # Fallback to in-memory cache
        self._fallback_cache.pop(key, None)
        self._fallback_expiry.pop(key, None)
        return True
    
    def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        try:
            client = self.redis_config.get_client()
            if client:
                return bool(client.exists(key))
        except Exception as e:
            logger.debug(f"Redis exists error: {e}")
        
        # Fallback to in-memory cache
        self._cleanup_fallback()
        return key in self._fallback_cache
    
    def clear(self) -> bool:
        """Clear all cache entries"""
        try:
            client = self.redis_config.get_client()
            if client:
                return client.flushdb()
        except Exception as e:
            logger.debug(f"Redis clear error: {e}")
        
        # Clear fallback cache
        self._fallback_cache.clear()
        self._fallback_expiry.clear()
        return True

class RedisSessionManager:
    """Redis-based session management with fallback"""
    
    def __init__(self, redis_config: RedisConfig):
        self.redis_config = redis_config
        self._fallback_sessions = {}  # In-memory fallback
    
    def get_session(self, session_id: str) -> Optional[Dict]:
        """Get session data"""
        try:
            client = self.redis_config.get_client()
            if client:
                data = client.hgetall(f"session:{session_id}")
                if data:
                    # Deserialize values
                    session = {}
                    for key, value in data.items():
                        try:
                            session[key] = json.loads(value)
                        except json.JSONDecodeError:
                            session[key] = value
                    return session
        except Exception as e:
            logger.debug(f"Redis session get error: {e}")
        
        # Fallback to in-memory sessions
        return self._fallback_sessions.get(session_id)
    
    def set_session(self, session_id: str, session_data: Dict, expire: int = 3600) -> bool:
        """Set session data"""
        try:
            client = self.redis_config.get_client()
            if client:
                # Serialize values
                serialized_data = {}
                for key, value in session_data.items():
                    if isinstance(value, (dict, list)):
                        serialized_data[key] = json.dumps(value)
                    else:
                        serialized_data[key] = str(value)
                
                client.hmset(f"session:{session_id}", serialized_data)
                client.expire(f"session:{session_id}", expire)
                return True
        except Exception as e:
            logger.debug(f"Redis session set error: {e}")
        
        # Fallback to in-memory sessions
        self._fallback_sessions[session_id] = session_data
        return True
    
    def delete_session(self, session_id: str) -> bool:
        """Delete session"""
        try:
            client = self.redis_config.get_client()
            if client:
                return bool(client.delete(f"session:{session_id}"))
        except Exception as e:
            logger.debug(f"Redis session delete error: {e}")
        
        # Fallback to in-memory sessions
        self._fallback_sessions.pop(session_id, None)
        return True

# Global instances
_redis_config = None
_redis_cache = None
_session_manager = None

def get_redis_config(config=None) -> RedisConfig:
    """Get global Redis configuration"""
    global _redis_config
    if _redis_config is None:
        _redis_config = RedisConfig(config)
    return _redis_config

def get_redis_cache(config=None) -> RedisCache:
    """Get global Redis cache instance"""
    global _redis_cache
    if _redis_cache is None:
        redis_config = get_redis_config(config)
        _redis_cache = RedisCache(redis_config)
    return _redis_cache

def get_session_manager(config=None) -> RedisSessionManager:
    """Get global session manager instance"""
    global _session_manager
    if _session_manager is None:
        redis_config = get_redis_config(config)
        _session_manager = RedisSessionManager(redis_config)
    return _session_manager

def test_redis_connection(config=None) -> Dict[str, Any]:
    """Test Redis connection and return status"""
    redis_config = get_redis_config(config)
    
    result = {
        'available': False,
        'host': redis_config.host,
        'port': redis_config.port,
        'error': None,
        'info': None
    }
    
    try:
        client = redis_config.get_client()
        if client:
            client.ping()
            info = client.info()
            result.update({
                'available': True,
                'info': {
                    'version': info.get('redis_version'),
                    'memory_used': info.get('used_memory_human'),
                    'connected_clients': info.get('connected_clients'),
                    'uptime': info.get('uptime_in_seconds')
                }
            })
    except Exception as e:
        result['error'] = str(e)
    
    return result
