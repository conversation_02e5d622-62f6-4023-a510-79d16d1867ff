"""
Multi-Channel Notification Service
==================================

Comprehensive notification system for sending tracking updates
via SMS, email, push notifications, and WebSocket.

Features:
1. Multi-channel notification delivery
2. Template-based messaging
3. User preference management
4. Delivery tracking and retry logic
5. Rate limiting and throttling
6. Integration with tracking system
"""

import logging
import json
import smtplib
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from enum import Enum
from dataclasses import dataclass, asdict
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from concurrent.futures import ThreadPoolExecutor, as_completed
import requests

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

logger = logging.getLogger(__name__)

# ============================================================================
# NOTIFICATION CONFIGURATION
# ============================================================================

class NotificationChannel(Enum):
    """Available notification channels"""
    EMAIL = "email"
    SMS = "sms"
    PUSH = "push"
    WEBSOCKET = "websocket"

class NotificationPriority(Enum):
    """Notification priority levels"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"

class NotificationStatus(Enum):
    """Notification delivery status"""
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    RETRY = "retry"

@dataclass
class NotificationTemplate:
    """Notification message template"""
    channel: NotificationChannel
    event_type: str
    subject_template: str
    body_template: str
    variables: List[str]

@dataclass
class NotificationPreferences:
    """User notification preferences"""
    user_id: Optional[int]
    email_enabled: bool = True
    sms_enabled: bool = True
    push_enabled: bool = True
    websocket_enabled: bool = True
    email_address: Optional[str] = None
    phone_number: Optional[str] = None
    push_token: Optional[str] = None

@dataclass
class NotificationRequest:
    """Notification request"""
    channel: NotificationChannel
    recipient: str
    subject: str
    message: str
    priority: NotificationPriority
    order_id: int
    tracking_number: str
    event_type: str
    metadata: Optional[Dict[str, Any]] = None

# ============================================================================
# NOTIFICATION TEMPLATES
# ============================================================================

class NotificationTemplates:
    """Predefined notification templates"""
    
    TEMPLATES = {
        # Order shipped notifications
        'order_shipped': {
            NotificationChannel.EMAIL: NotificationTemplate(
                channel=NotificationChannel.EMAIL,
                event_type='order_shipped',
                subject_template='Your order #{order_id} has been shipped!',
                body_template='''
                Dear Customer,
                
                Great news! Your order #{order_id} has been shipped and is on its way to you.
                
                Tracking Details:
                - Tracking Number: {tracking_number}
                - Carrier: {carrier_name}
                - Estimated Delivery: {estimated_delivery}
                
                You can track your shipment at: {tracking_url}
                
                Thank you for shopping with Allora!
                
                Best regards,
                The Allora Team
                ''',
                variables=['order_id', 'tracking_number', 'carrier_name', 'estimated_delivery', 'tracking_url']
            ),
            NotificationChannel.SMS: NotificationTemplate(
                channel=NotificationChannel.SMS,
                event_type='order_shipped',
                subject_template='',
                body_template='Your Allora order #{order_id} has shipped! Track: {tracking_number}. Est. delivery: {estimated_delivery}. Track at: {tracking_url}',
                variables=['order_id', 'tracking_number', 'estimated_delivery', 'tracking_url']
            )
        },
        
        # Out for delivery notifications
        'out_for_delivery': {
            NotificationChannel.EMAIL: NotificationTemplate(
                channel=NotificationChannel.EMAIL,
                event_type='out_for_delivery',
                subject_template='Your order #{order_id} is out for delivery!',
                body_template='''
                Dear Customer,
                
                Your order #{order_id} is out for delivery and should arrive today!
                
                Tracking Number: {tracking_number}
                Expected Delivery: {estimated_delivery}
                
                Please ensure someone is available to receive the package.
                
                Track your shipment: {tracking_url}
                
                Best regards,
                The Allora Team
                ''',
                variables=['order_id', 'tracking_number', 'estimated_delivery', 'tracking_url']
            ),
            NotificationChannel.SMS: NotificationTemplate(
                channel=NotificationChannel.SMS,
                event_type='out_for_delivery',
                subject_template='',
                body_template='Your Allora order #{order_id} is out for delivery! Expected today. Tracking: {tracking_number}',
                variables=['order_id', 'tracking_number']
            )
        },
        
        # Delivered notifications
        'delivered': {
            NotificationChannel.EMAIL: NotificationTemplate(
                channel=NotificationChannel.EMAIL,
                event_type='delivered',
                subject_template='Your order #{order_id} has been delivered!',
                body_template='''
                Dear Customer,
                
                Great news! Your order #{order_id} has been successfully delivered.
                
                Delivery Details:
                - Tracking Number: {tracking_number}
                - Delivered At: {delivery_time}
                - Location: {delivery_location}
                
                We hope you love your purchase! Please consider leaving a review.
                
                Thank you for choosing Allora!
                
                Best regards,
                The Allora Team
                ''',
                variables=['order_id', 'tracking_number', 'delivery_time', 'delivery_location']
            ),
            NotificationChannel.SMS: NotificationTemplate(
                channel=NotificationChannel.SMS,
                event_type='delivered',
                subject_template='',
                body_template='Your Allora order #{order_id} has been delivered! Thank you for shopping with us.',
                variables=['order_id']
            )
        },
        
        # Exception notifications
        'delivery_exception': {
            NotificationChannel.EMAIL: NotificationTemplate(
                channel=NotificationChannel.EMAIL,
                event_type='delivery_exception',
                subject_template='Update on your order #{order_id}',
                body_template='''
                Dear Customer,
                
                We wanted to update you on your order #{order_id}.
                
                There has been an issue with delivery:
                {exception_description}
                
                Tracking Number: {tracking_number}
                
                Our team is working to resolve this. We'll keep you updated.
                
                For immediate assistance, please contact our support team.
                
                Best regards,
                The Allora Team
                ''',
                variables=['order_id', 'tracking_number', 'exception_description']
            ),
            NotificationChannel.SMS: NotificationTemplate(
                channel=NotificationChannel.SMS,
                event_type='delivery_exception',
                subject_template='',
                body_template='Update on Allora order #{order_id}: {exception_description}. We\'re working to resolve this.',
                variables=['order_id', 'exception_description']
            )
        }
    }
    
    @classmethod
    def get_template(cls, event_type: str, channel: NotificationChannel) -> Optional[NotificationTemplate]:
        """Get notification template for event type and channel"""
        return cls.TEMPLATES.get(event_type, {}).get(channel)

# ============================================================================
# NOTIFICATION CHANNELS
# ============================================================================

class EmailNotificationChannel:
    """Email notification channel"""
    
    def __init__(self, smtp_server: str, smtp_port: int, username: str, password: str):
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
    
    def send_notification(self, request: NotificationRequest) -> bool:
        """Send email notification"""
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.username
            msg['To'] = request.recipient
            msg['Subject'] = request.subject
            
            # Add body
            msg.attach(MIMEText(request.message, 'plain'))
            
            # Send email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.username, self.password)
                server.send_message(msg)
            
            logger.info(f"Email sent to {request.recipient}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending email to {request.recipient}: {e}")
            return False

class SMSNotificationChannel:
    """SMS notification channel using Twilio or similar service"""
    
    def __init__(self, api_key: str, api_secret: str, sender_number: str):
        self.api_key = api_key
        self.api_secret = api_secret
        self.sender_number = sender_number
    
    def send_notification(self, request: NotificationRequest) -> bool:
        """Send SMS notification"""
        try:
            # Example using a generic SMS API
            payload = {
                'from': self.sender_number,
                'to': request.recipient,
                'message': request.message,
                'api_key': self.api_key
            }
            
            # Make API call (replace with actual SMS service)
            response = requests.post(
                'https://api.smsservice.com/send',
                json=payload,
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info(f"SMS sent to {request.recipient}")
                return True
            else:
                logger.error(f"SMS API error: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending SMS to {request.recipient}: {e}")
            return False

class PushNotificationChannel:
    """Push notification channel using Firebase or similar service"""
    
    def __init__(self, server_key: str):
        self.server_key = server_key
    
    def send_notification(self, request: NotificationRequest) -> bool:
        """Send push notification"""
        try:
            headers = {
                'Authorization': f'key={self.server_key}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                'to': request.recipient,  # FCM token
                'notification': {
                    'title': request.subject,
                    'body': request.message,
                    'icon': 'allora_icon',
                    'click_action': 'FLUTTER_NOTIFICATION_CLICK'
                },
                'data': {
                    'order_id': str(request.order_id),
                    'tracking_number': request.tracking_number,
                    'event_type': request.event_type
                }
            }
            
            response = requests.post(
                'https://fcm.googleapis.com/fcm/send',
                headers=headers,
                json=payload,
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info(f"Push notification sent to {request.recipient}")
                return True
            else:
                logger.error(f"Push notification API error: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending push notification to {request.recipient}: {e}")
            return False

class WebSocketNotificationChannel:
    """WebSocket notification channel"""
    
    def __init__(self):
        pass
    
    def send_notification(self, request: NotificationRequest) -> bool:
        """Send WebSocket notification"""
        try:
            # FIXED: Use Flask-SocketIO instead of deprecated websocket_manager
            from flask_socketio_manager import send_notification, socketio_manager

            # Send to specific user if user_id is available
            if request.metadata and 'user_id' in request.metadata:
                user_id = request.metadata['user_id']
                send_notification(user_id, {
                    'type': 'tracking_update',
                    'order_id': request.order_id,
                    'tracking_number': request.tracking_number,
                    'event_type': request.event_type,
                    'message': request.message,
                    'timestamp': datetime.now().isoformat()
                })
            else:
                # Broadcast to all connected clients
                if socketio_manager and socketio_manager.socketio:
                    socketio_manager.socketio.emit('tracking_update', {
                        'type': 'tracking_update',
                        'order_id': request.order_id,
                        'tracking_number': request.tracking_number,
                        'event_type': request.event_type,
                        'message': request.message,
                        'timestamp': datetime.now().isoformat()
                    })
            
            logger.info(f"WebSocket notification sent for order {request.order_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending WebSocket notification: {e}")
            return False

# ============================================================================
# NOTIFICATION SERVICE
# ============================================================================

class NotificationService:
    """Main notification service"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.channels = {}
        self.templates = NotificationTemplates()
        self.delivery_queue = []
        self.failed_notifications = []
        self.rate_limits = {}  # Channel -> last_sent_time
        self.rate_limit_window = 60  # 1 minute
        self.max_notifications_per_window = 100
        
        # Initialize notification channels
        self._initialize_channels()
        
        # Start background delivery thread
        self._start_delivery_thread()
    
    def _initialize_channels(self):
        """Initialize notification channels"""
        try:
            # Email channel (configure with your SMTP settings)
            self.channels[NotificationChannel.EMAIL] = EmailNotificationChannel(
                smtp_server='smtp.gmail.com',
                smtp_port=587,
                username='<EMAIL>',
                password='your-app-password'
            )
            
            # SMS channel (configure with your SMS service)
            self.channels[NotificationChannel.SMS] = SMSNotificationChannel(
                api_key='your-sms-api-key',
                api_secret='your-sms-api-secret',
                sender_number='+1234567890'
            )
            
            # Push notification channel (configure with your FCM key)
            self.channels[NotificationChannel.PUSH] = PushNotificationChannel(
                server_key='your-fcm-server-key'
            )
            
            # WebSocket channel
            self.channels[NotificationChannel.WEBSOCKET] = WebSocketNotificationChannel()
            
        except Exception as e:
            logger.error(f"Error initializing notification channels: {e}")
    
    def _start_delivery_thread(self):
        """Start background thread for notification delivery"""
        def delivery_worker():
            while True:
                try:
                    if self.delivery_queue:
                        # Process notifications in queue
                        notifications_to_process = self.delivery_queue[:10]  # Process 10 at a time
                        self.delivery_queue = self.delivery_queue[10:]
                        
                        with ThreadPoolExecutor(max_workers=5) as executor:
                            futures = [
                                executor.submit(self._deliver_notification, notification)
                                for notification in notifications_to_process
                            ]
                            
                            for future in as_completed(futures):
                                try:
                                    future.result()
                                except Exception as e:
                                    logger.error(f"Error in notification delivery: {e}")
                    
                    time.sleep(5)  # Check queue every 5 seconds
                    
                except Exception as e:
                    logger.error(f"Error in delivery worker: {e}")
                    time.sleep(30)
        
        delivery_thread = threading.Thread(target=delivery_worker, daemon=True)
        delivery_thread.start()
        logger.info("Notification delivery service started")
    
    def send_tracking_notification(self, order_id: int, tracking_number: str, tracking_event) -> bool:
        """Send tracking notification based on event"""
        try:
            # Determine event type for template selection
            event_type = self._map_tracking_event_to_notification_type(tracking_event.status)
            
            if not event_type:
                return True  # No notification needed for this event
            
            # Get user preferences
            preferences = self._get_user_preferences(order_id)
            
            if not preferences:
                logger.warning(f"No user preferences found for order {order_id}")
                return False
            
            # Create notifications for enabled channels
            notifications_created = 0
            
            for channel in NotificationChannel:
                if self._is_channel_enabled(channel, preferences):
                    recipient = self._get_recipient_for_channel(channel, preferences)
                    
                    if recipient:
                        notification = self._create_notification(
                            channel, event_type, order_id, tracking_number, 
                            tracking_event, recipient, preferences
                        )
                        
                        if notification:
                            self.delivery_queue.append(notification)
                            notifications_created += 1
            
            logger.info(f"Created {notifications_created} notifications for order {order_id}")
            return notifications_created > 0
            
        except Exception as e:
            logger.error(f"Error sending tracking notification: {e}")
            return False
    
    def _map_tracking_event_to_notification_type(self, status) -> Optional[str]:
        """Map tracking status to notification event type"""
        from tracking_system import TrackingStatus
        
        mapping = {
            TrackingStatus.PICKED_UP: 'order_shipped',
            TrackingStatus.OUT_FOR_DELIVERY: 'out_for_delivery',
            TrackingStatus.DELIVERED: 'delivered',
            TrackingStatus.DELIVERY_ATTEMPTED: 'delivery_exception',
            TrackingStatus.EXCEPTION: 'delivery_exception',
            TrackingStatus.RETURNED_TO_SENDER: 'delivery_exception',
            TrackingStatus.LOST: 'delivery_exception',
            TrackingStatus.DAMAGED: 'delivery_exception'
        }
        
        return mapping.get(status)
    
    def _get_user_preferences(self, order_id: int) -> Optional[NotificationPreferences]:
        """Get user notification preferences for order"""
        try:
            from app import Order, User
            
            # Get order and user information
            order = self.db.query(Order).filter(Order.id == order_id).first()
            
            if not order:
                return None
            
            # For guest orders, use order email/phone
            if not order.user_id:
                return NotificationPreferences(
                    user_id=None,
                    email_enabled=bool(order.email),
                    sms_enabled=bool(order.phone),
                    push_enabled=False,
                    websocket_enabled=False,
                    email_address=order.email,
                    phone_number=order.phone
                )
            
            # For registered users, get user preferences
            user = self.db.query(User).filter(User.id == order.user_id).first()
            
            if not user:
                return None
            
            return NotificationPreferences(
                user_id=user.id,
                email_enabled=True,
                sms_enabled=True,
                push_enabled=True,
                websocket_enabled=True,
                email_address=user.email,
                phone_number=getattr(user, 'phone', None)
            )
            
        except Exception as e:
            logger.error(f"Error getting user preferences: {e}")
            return None
    
    def _is_channel_enabled(self, channel: NotificationChannel, preferences: NotificationPreferences) -> bool:
        """Check if notification channel is enabled for user"""
        channel_enabled = {
            NotificationChannel.EMAIL: preferences.email_enabled,
            NotificationChannel.SMS: preferences.sms_enabled,
            NotificationChannel.PUSH: preferences.push_enabled,
            NotificationChannel.WEBSOCKET: preferences.websocket_enabled
        }
        
        return channel_enabled.get(channel, False)
    
    def _get_recipient_for_channel(self, channel: NotificationChannel, preferences: NotificationPreferences) -> Optional[str]:
        """Get recipient address for notification channel"""
        recipients = {
            NotificationChannel.EMAIL: preferences.email_address,
            NotificationChannel.SMS: preferences.phone_number,
            NotificationChannel.PUSH: preferences.push_token,
            NotificationChannel.WEBSOCKET: str(preferences.user_id) if preferences.user_id else 'guest'
        }
        
        return recipients.get(channel)
    
    def _create_notification(self, channel: NotificationChannel, event_type: str, 
                           order_id: int, tracking_number: str, tracking_event, 
                           recipient: str, preferences: NotificationPreferences) -> Optional[NotificationRequest]:
        """Create notification request"""
        try:
            # Get template
            template = self.templates.get_template(event_type, channel)
            
            if not template:
                logger.warning(f"No template found for {event_type} on {channel.value}")
                return None
            
            # Prepare template variables
            variables = {
                'order_id': order_id,
                'tracking_number': tracking_number,
                'carrier_name': tracking_event.carrier_code.title(),
                'estimated_delivery': tracking_event.estimated_delivery.strftime('%B %d, %Y') if tracking_event.estimated_delivery else 'TBD',
                'delivery_time': tracking_event.timestamp.strftime('%B %d, %Y at %I:%M %p'),
                'delivery_location': tracking_event.location or 'Your address',
                'exception_description': tracking_event.exception_description or tracking_event.description or 'Delivery issue occurred',
                'tracking_url': f'https://allora.com/track/{tracking_number}'
            }
            
            # Format subject and message
            subject = template.subject_template.format(**variables)
            message = template.body_template.format(**variables)
            
            return NotificationRequest(
                channel=channel,
                recipient=recipient,
                subject=subject,
                message=message,
                priority=NotificationPriority.NORMAL,
                order_id=order_id,
                tracking_number=tracking_number,
                event_type=event_type,
                metadata={'user_id': preferences.user_id}
            )
            
        except Exception as e:
            logger.error(f"Error creating notification: {e}")
            return None
    
    def _deliver_notification(self, notification: NotificationRequest) -> bool:
        """Deliver a single notification"""
        try:
            # Check rate limits
            if not self._check_rate_limit(notification.channel):
                logger.warning(f"Rate limit exceeded for {notification.channel.value}")
                return False
            
            # Get channel handler
            channel_handler = self.channels.get(notification.channel)
            
            if not channel_handler:
                logger.error(f"No handler for channel {notification.channel.value}")
                return False
            
            # Send notification
            success = channel_handler.send_notification(notification)
            
            if success:
                self._update_rate_limit(notification.channel)
                logger.info(f"Notification delivered via {notification.channel.value} to {notification.recipient}")
            else:
                self.failed_notifications.append(notification)
                logger.error(f"Failed to deliver notification via {notification.channel.value}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error delivering notification: {e}")
            return False
    
    def _check_rate_limit(self, channel: NotificationChannel) -> bool:
        """Check if channel is within rate limits"""
        now = time.time()
        channel_key = channel.value
        
        if channel_key not in self.rate_limits:
            return True
        
        last_sent, count = self.rate_limits[channel_key]
        
        # Reset counter if window has passed
        if now - last_sent > self.rate_limit_window:
            return True
        
        return count < self.max_notifications_per_window
    
    def _update_rate_limit(self, channel: NotificationChannel):
        """Update rate limit counter for channel"""
        now = time.time()
        channel_key = channel.value
        
        if channel_key not in self.rate_limits:
            self.rate_limits[channel_key] = (now, 1)
        else:
            last_sent, count = self.rate_limits[channel_key]
            
            if now - last_sent > self.rate_limit_window:
                self.rate_limits[channel_key] = (now, 1)
            else:
                self.rate_limits[channel_key] = (last_sent, count + 1)

# ============================================================================
# FACTORY AND INTEGRATION
# ============================================================================

# Global notification service instance
_global_notification_service = None
_notification_service_lock = threading.Lock()

def get_notification_service(db_session: Session = None) -> NotificationService:
    """Get global notification service instance"""
    global _global_notification_service

    if _global_notification_service is None:
        with _notification_service_lock:
            if _global_notification_service is None:
                if db_session is None:
                    from app import db
                    db_session = db.session
                _global_notification_service = NotificationService(db_session)

    return _global_notification_service

def create_notification_service(db_session: Session) -> NotificationService:
    """Create a new notification service instance"""
    return NotificationService(db_session)
