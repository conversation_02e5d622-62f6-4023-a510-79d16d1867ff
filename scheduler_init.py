"""
Scheduler Initialization Module
==============================

This module handles the initialization of background schedulers and tasks
without creating circular dependencies with the main application.

Author: Allora Development Team
Date: 2025-07-07
"""

import logging
import threading
import time
from typing import Optional

logger = logging.getLogger(__name__)

class SchedulerManager:
    """Manages all background schedulers and tasks"""
    
    def __init__(self):
        self.schedulers = {}
        self.running = False
        self._init_thread = None
    
    def register_scheduler(self, name: str, scheduler_class, *args, **kwargs):
        """Register a scheduler to be initialized later"""
        self.schedulers[name] = {
            'class': scheduler_class,
            'args': args,
            'kwargs': kwargs,
            'instance': None,
            'initialized': False
        }
        logger.info(f"Registered scheduler: {name}")
    
    def initialize_all(self, delay: int = 5):
        """Initialize all registered schedulers with a delay"""
        if self._init_thread and self._init_thread.is_alive():
            logger.warning("Scheduler initialization already in progress")
            return
        
        self._init_thread = threading.Thread(
            target=self._delayed_init,
            args=(delay,),
            daemon=True
        )
        self._init_thread.start()
        logger.info(f"Scheduler initialization will start in {delay} seconds")
    
    def _delayed_init(self, delay: int):
        """Initialize schedulers after a delay to avoid circular imports"""
        time.sleep(delay)
        
        logger.info("Starting scheduler initialization...")
        
        for name, config in self.schedulers.items():
            try:
                logger.info(f"Initializing scheduler: {name}")
                
                # Create scheduler instance
                scheduler_instance = config['class'](*config['args'], **config['kwargs'])
                config['instance'] = scheduler_instance
                
                # Start the scheduler if it has a start method
                if hasattr(scheduler_instance, 'start'):
                    scheduler_instance.start()
                    logger.info(f"✅ Started scheduler: {name}")
                else:
                    logger.info(f"✅ Initialized scheduler: {name}")
                
                config['initialized'] = True
                
            except Exception as e:
                logger.error(f"❌ Failed to initialize scheduler {name}: {e}")
                config['initialized'] = False
        
        self.running = True
        logger.info("Scheduler initialization completed")
    
    def get_scheduler(self, name: str) -> Optional[object]:
        """Get a scheduler instance by name"""
        config = self.schedulers.get(name)
        if config and config['initialized']:
            return config['instance']
        return None
    
    def stop_all(self):
        """Stop all running schedulers"""
        logger.info("Stopping all schedulers...")
        
        for name, config in self.schedulers.items():
            if config['initialized'] and config['instance']:
                try:
                    if hasattr(config['instance'], 'stop'):
                        config['instance'].stop()
                        logger.info(f"Stopped scheduler: {name}")
                except Exception as e:
                    logger.error(f"Error stopping scheduler {name}: {e}")
        
        self.running = False
        logger.info("All schedulers stopped")
    
    def get_status(self) -> dict:
        """Get status of all schedulers"""
        status = {
            'manager_running': self.running,
            'schedulers': {}
        }
        
        for name, config in self.schedulers.items():
            scheduler_status = {
                'registered': True,
                'initialized': config['initialized'],
                'running': False
            }
            
            if config['instance'] and hasattr(config['instance'], 'running'):
                scheduler_status['running'] = config['instance'].running
            
            status['schedulers'][name] = scheduler_status
        
        return status

# Global scheduler manager instance
_scheduler_manager = None

def get_scheduler_manager() -> SchedulerManager:
    """Get the global scheduler manager instance"""
    global _scheduler_manager
    if _scheduler_manager is None:
        _scheduler_manager = SchedulerManager()
    return _scheduler_manager

def register_inventory_scheduler():
    """Register the inventory scheduler for delayed initialization"""
    try:
        # Import here to avoid circular dependencies
        from inventory_scheduler import InventorySyncScheduler

        manager = get_scheduler_manager()
        manager.register_scheduler('inventory_sync', InventorySyncScheduler)
        logger.info("Inventory scheduler registered successfully")

    except ImportError as e:
        logger.warning(f"Could not import inventory scheduler: {e}")
    except NameError as e:
        logger.warning(f"Missing dependency for inventory scheduler: {e}")
    except Exception as e:
        logger.error(f"Error registering inventory scheduler: {e}")

def initialize_schedulers(app=None, delay: int = 5):
    """Initialize all schedulers with Flask app context"""
    try:
        # Register all schedulers
        register_inventory_scheduler()
        
        # Initialize with delay
        manager = get_scheduler_manager()
        manager.initialize_all(delay)
        
        logger.info("Scheduler initialization process started")
        
    except Exception as e:
        logger.error(f"Error in scheduler initialization: {e}")

def get_scheduler_status():
    """Get status of all schedulers"""
    manager = get_scheduler_manager()
    return manager.get_status()

def stop_all_schedulers():
    """Stop all running schedulers"""
    manager = get_scheduler_manager()
    manager.stop_all()

# Flask integration functions
def init_schedulers_with_app(app):
    """Initialize schedulers with Flask app"""
    @app.before_first_request
    def setup_schedulers():
        initialize_schedulers(app)
    
    # Also register cleanup on app teardown
    @app.teardown_appcontext
    def cleanup_schedulers(error):
        if error:
            logger.error(f"App context error: {error}")

def create_scheduler_blueprint():
    """Create a Flask blueprint for scheduler management"""
    from flask import Blueprint, jsonify
    
    scheduler_bp = Blueprint('scheduler', __name__, url_prefix='/api/admin/scheduler')
    
    @scheduler_bp.route('/status', methods=['GET'])
    def scheduler_status():
        """Get scheduler status endpoint"""
        try:
            status = get_scheduler_status()
            return jsonify({
                'success': True,
                'data': status
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @scheduler_bp.route('/restart', methods=['POST'])
    def restart_schedulers():
        """Restart all schedulers endpoint"""
        try:
            stop_all_schedulers()
            time.sleep(2)  # Brief pause
            initialize_schedulers(delay=1)
            
            return jsonify({
                'success': True,
                'message': 'Schedulers restart initiated'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    return scheduler_bp

# Export main functions
__all__ = [
    'SchedulerManager',
    'get_scheduler_manager',
    'initialize_schedulers',
    'get_scheduler_status',
    'stop_all_schedulers',
    'init_schedulers_with_app',
    'create_scheduler_blueprint'
]
