"""
User Behavior Tracking System
============================

Comprehensive system for tracking and analyzing user interactions in real-time.
Captures user behavior patterns for personalized recommendations.

Features:
- Real-time interaction tracking
- Session management
- Behavioral pattern analysis
- User preference learning
- Cross-device tracking support

Author: Allora Development Team
Date: 2025-07-06
"""

import json
import time
import uuid
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import redis
from sqlalchemy.engine import make_url
from sqlalchemy.orm import sessionmaker

# Add backend path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

# Import architecture components
from .recommendation_system_architecture import (
    UserInteraction, UserInteractionType, UserProfile
)

class BehaviorTracker:
    """
    Main class for tracking user behavior and interactions
    """
    
    def __init__(self, db_session, redis_client=None):
        self.db_session = db_session
        self.redis_client = redis_client or redis.Redis(host='localhost', port=6379, db=0)
        
        # Configuration
        self.session_timeout = 1800  # 30 minutes
        self.interaction_buffer_size = 1000
        self.profile_update_threshold = 5  # Update profile after N interactions
        
        # In-memory buffers for performance
        self.interaction_buffer = deque(maxlen=self.interaction_buffer_size)
        self.session_data = {}
        
        # Interaction weights for preference calculation
        self.interaction_weights = {
            UserInteractionType.VIEW: 1.0,
            UserInteractionType.CLICK: 2.0,
            UserInteractionType.ADD_TO_CART: 3.0,
            UserInteractionType.REMOVE_FROM_CART: 1.0,
            UserInteractionType.PURCHASE: 5.0,
            UserInteractionType.RATING: 4.0,
            UserInteractionType.REVIEW: 4.0,
            UserInteractionType.WISHLIST_ADD: 3.0,
            UserInteractionType.WISHLIST_REMOVE: 1.0,
            UserInteractionType.SEARCH: 1.5,
            UserInteractionType.SHARE: 2.5,
            UserInteractionType.COMPARE: 2.0  # High intent - users comparing are likely to purchase
        }
    
    def track_interaction(self, user_id: str, product_id: str, 
                         interaction_type: UserInteractionType,
                         session_id: Optional[str] = None,
                         value: Optional[float] = None,
                         context: Optional[Dict[str, Any]] = None,
                         metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Track a user interaction with a product
        
        Args:
            user_id: User identifier
            product_id: Product identifier
            interaction_type: Type of interaction
            session_id: Session identifier (auto-generated if not provided)
            value: Optional value (rating, price, etc.)
            context: Additional context information
            metadata: Additional metadata
            
        Returns:
            Interaction ID
        """
        try:
            # Generate session ID if not provided
            if not session_id:
                session_id = self._get_or_create_session(user_id)
            
            # Create interaction object
            interaction = UserInteraction(
                user_id=user_id,
                product_id=product_id,
                interaction_type=interaction_type,
                timestamp=datetime.utcnow(),
                session_id=session_id,
                value=value,
                context=context or {},
                metadata=metadata or {}
            )
            
            # Generate unique interaction ID
            interaction_id = str(uuid.uuid4())
            
            # Store in buffer for batch processing
            self.interaction_buffer.append((interaction_id, interaction))
            
            # Store in Redis for real-time access
            self._store_interaction_redis(interaction_id, interaction)
            
            # Update session data
            self._update_session_data(session_id, interaction)
            
            # Trigger real-time profile update if threshold reached
            self._check_profile_update_trigger(user_id)
            
            # Process interaction for immediate insights
            self._process_real_time_interaction(interaction)
            
            return interaction_id
            
        except Exception as e:
            print(f"Error tracking interaction: {e}")
            return None
    
    def track_page_view(self, user_id: str, page_type: str, page_id: str,
                       session_id: Optional[str] = None,
                       duration: Optional[float] = None,
                       context: Optional[Dict[str, Any]] = None) -> str:
        """Track page view interactions"""
        
        context = context or {}
        context.update({
            'page_type': page_type,
            'page_id': page_id,
            'duration': duration
        })
        
        return self.track_interaction(
            user_id=user_id,
            product_id=page_id if page_type == 'product' else 'page_view',
            interaction_type=UserInteractionType.VIEW,
            session_id=session_id,
            context=context
        )
    
    def track_search_interaction(self, user_id: str, search_query: str,
                               results_count: int, clicked_products: List[str] = None,
                               session_id: Optional[str] = None) -> str:
        """Track search interactions"""
        
        context = {
            'search_query': search_query,
            'results_count': results_count,
            'clicked_products': clicked_products or []
        }
        
        return self.track_interaction(
            user_id=user_id,
            product_id='search_interaction',
            interaction_type=UserInteractionType.SEARCH,
            session_id=session_id,
            context=context
        )
    
    def track_cart_interaction(self, user_id: str, product_id: str,
                             action: str, quantity: int = 1,
                             session_id: Optional[str] = None) -> str:
        """Track cart-related interactions"""
        
        interaction_type = (
            UserInteractionType.ADD_TO_CART if action == 'add'
            else UserInteractionType.REMOVE_FROM_CART
        )
        
        context = {
            'action': action,
            'quantity': quantity
        }
        
        return self.track_interaction(
            user_id=user_id,
            product_id=product_id,
            interaction_type=interaction_type,
            session_id=session_id,
            value=float(quantity),
            context=context
        )
    
    def track_purchase(self, user_id: str, order_id: str, products: List[Dict[str, Any]],
                      total_amount: float, session_id: Optional[str] = None) -> List[str]:
        """Track purchase interactions for multiple products"""
        
        interaction_ids = []
        
        for product_data in products:
            product_id = product_data.get('product_id')
            quantity = product_data.get('quantity', 1)
            unit_price = product_data.get('unit_price', 0.0)
            
            context = {
                'order_id': order_id,
                'quantity': quantity,
                'unit_price': unit_price,
                'total_amount': total_amount
            }
            
            interaction_id = self.track_interaction(
                user_id=user_id,
                product_id=str(product_id),
                interaction_type=UserInteractionType.PURCHASE,
                session_id=session_id,
                value=unit_price * quantity,
                context=context
            )
            
            if interaction_id:
                interaction_ids.append(interaction_id)
        
        return interaction_ids

    def track_compare_interaction(self, user_id: str, product_ids: List[str],
                                 comparison_type: str = 'product_comparison',
                                 session_id: Optional[str] = None,
                                 context: Optional[Dict[str, Any]] = None) -> List[str]:
        """Track product comparison interactions"""

        interaction_ids = []

        # Enhanced context for comparison tracking
        compare_context = {
            'comparison_type': comparison_type,
            'products_compared': product_ids,
            'comparison_count': len(product_ids),
            'timestamp': datetime.utcnow().isoformat()
        }

        if context:
            compare_context.update(context)

        # Track comparison interaction for each product
        for product_id in product_ids:
            interaction_id = self.track_interaction(
                user_id=user_id,
                product_id=str(product_id),
                interaction_type=UserInteractionType.COMPARE,
                session_id=session_id,
                value=float(len(product_ids)),  # Number of products being compared
                context=compare_context
            )

            if interaction_id:
                interaction_ids.append(interaction_id)

        return interaction_ids

    def get_user_interactions(self, user_id: str, limit: int = 100,
                             interaction_types: List[UserInteractionType] = None,
                             time_range: Tuple[datetime, datetime] = None) -> List[UserInteraction]:
        """Get user interactions with optional filtering"""
        try:
            interactions = []

            # Get from Redis first (recent interactions)
            redis_keys = self.redis_client.keys(f"interaction:{user_id}:*")

            for key in redis_keys:
                interaction_data = self.redis_client.hgetall(key)
                if interaction_data:
                    # Reconstruct interaction object
                    interaction = UserInteraction(
                        user_id=interaction_data[b'user_id'].decode(),
                        product_id=interaction_data[b'product_id'].decode(),
                        interaction_type=UserInteractionType(interaction_data[b'interaction_type'].decode()),
                        timestamp=datetime.fromisoformat(interaction_data[b'timestamp'].decode()),
                        session_id=interaction_data[b'session_id'].decode(),
                        value=float(interaction_data[b'value'].decode()) if interaction_data.get(b'value') else None,
                        context=json.loads(interaction_data[b'context'].decode()) if interaction_data.get(b'context') else {},
                        metadata=json.loads(interaction_data[b'metadata'].decode()) if interaction_data.get(b'metadata') else {}
                    )

                    # Apply filters
                    if interaction_types and interaction.interaction_type not in interaction_types:
                        continue

                    if time_range:
                        start_time, end_time = time_range
                        if not (start_time <= interaction.timestamp <= end_time):
                            continue

                    interactions.append(interaction)

            # Sort by timestamp (most recent first)
            interactions.sort(key=lambda x: x.timestamp, reverse=True)
            return interactions[:limit]

        except Exception as e:
            print(f"Error getting user interactions: {e}")
            return []

    def analyze_user_behavior(self, user_id: str) -> Dict[str, Any]:
        """Analyze user behavior patterns"""
        try:
            interactions = self.get_user_interactions(user_id, limit=500)

            if not interactions:
                return {'error': 'No interactions found'}

            # Basic statistics
            total_interactions = len(interactions)
            interaction_types = defaultdict(int)
            products_interacted = set()
            categories_interacted = defaultdict(int)
            brands_interacted = defaultdict(int)

            # Time-based analysis
            hourly_activity = defaultdict(int)
            daily_activity = defaultdict(int)

            # Value analysis
            total_value = 0.0
            purchase_count = 0

            for interaction in interactions:
                interaction_types[interaction.interaction_type.value] += 1
                products_interacted.add(interaction.product_id)

                # Time analysis
                hour = interaction.timestamp.hour
                day = interaction.timestamp.strftime('%A')
                hourly_activity[hour] += 1
                daily_activity[day] += 1

                # Value analysis
                if interaction.value:
                    total_value += interaction.value

                if interaction.interaction_type == UserInteractionType.PURCHASE:
                    purchase_count += 1

                # Category and brand analysis from context
                context = interaction.context or {}
                if 'category' in context:
                    categories_interacted[context['category']] += 1
                if 'brand' in context:
                    brands_interacted[context['brand']] += 1

            # Calculate engagement metrics
            avg_session_length = self._calculate_avg_session_length(user_id)
            engagement_score = self._calculate_engagement_score(interactions)

            return {
                'user_id': user_id,
                'total_interactions': total_interactions,
                'unique_products': len(products_interacted),
                'interaction_breakdown': dict(interaction_types),
                'top_categories': dict(sorted(categories_interacted.items(),
                                            key=lambda x: x[1], reverse=True)[:5]),
                'top_brands': dict(sorted(brands_interacted.items(),
                                        key=lambda x: x[1], reverse=True)[:5]),
                'activity_patterns': {
                    'hourly': dict(hourly_activity),
                    'daily': dict(daily_activity)
                },
                'purchase_metrics': {
                    'total_purchases': purchase_count,
                    'total_value': total_value,
                    'avg_order_value': total_value / purchase_count if purchase_count > 0 else 0
                },
                'engagement_metrics': {
                    'avg_session_length': avg_session_length,
                    'engagement_score': engagement_score
                },
                'last_activity': interactions[0].timestamp.isoformat() if interactions else None
            }

        except Exception as e:
            print(f"Error analyzing user behavior: {e}")
            return {'error': str(e)}

    def _get_or_create_session(self, user_id: str) -> str:
        """Get existing session or create new one"""
        try:
            # Check for active session in Redis
            active_sessions = self.redis_client.keys(f"session:{user_id}:*")

            for session_key in active_sessions:
                session_data = self.redis_client.hget(session_key, 'last_activity')
                if session_data:
                    last_activity = datetime.fromisoformat(session_data.decode())
                    if datetime.utcnow() - last_activity < timedelta(seconds=self.session_timeout):
                        return session_key.decode().split(':')[-1]

            # Create new session
            session_id = str(uuid.uuid4())
            self._initialize_session(user_id, session_id)
            return session_id

        except Exception as e:
            print(f"Error managing session: {e}")
            return str(uuid.uuid4())

    def _initialize_session(self, user_id: str, session_id: str):
        """Initialize a new user session"""
        session_data = {
            'user_id': user_id,
            'session_id': session_id,
            'start_time': datetime.utcnow().isoformat(),
            'last_activity': datetime.utcnow().isoformat(),
            'interaction_count': 0,
            'pages_visited': [],
            'products_viewed': [],
            'search_queries': []
        }

        session_key = f"session:{user_id}:{session_id}"
        self.redis_client.hmset(session_key, session_data)
        self.redis_client.expire(session_key, self.session_timeout)

    def _store_interaction_redis(self, interaction_id: str, interaction: UserInteraction):
        """Store interaction in Redis for real-time access"""
        try:
            interaction_key = f"interaction:{interaction.user_id}:{interaction_id}"
            interaction_data = {
                'user_id': interaction.user_id,
                'product_id': interaction.product_id,
                'interaction_type': interaction.interaction_type.value,
                'timestamp': interaction.timestamp.isoformat(),
                'session_id': interaction.session_id,
                'value': str(interaction.value) if interaction.value else '',
                'context': json.dumps(interaction.context),
                'metadata': json.dumps(interaction.metadata)
            }

            self.redis_client.hmset(interaction_key, interaction_data)
            self.redis_client.expire(interaction_key, 86400)  # 24 hours

        except Exception as e:
            print(f"Error storing interaction in Redis: {e}")

    def _update_session_data(self, session_id: str, interaction: UserInteraction):
        """Update session data with new interaction"""
        try:
            session_key = f"session:{interaction.user_id}:{session_id}"

            # Update session activity
            self.redis_client.hset(session_key, 'last_activity', datetime.utcnow().isoformat())
            self.redis_client.hincrby(session_key, 'interaction_count', 1)

            # Add to session context
            if interaction.interaction_type == UserInteractionType.VIEW:
                if interaction.product_id != 'page_view':
                    self.redis_client.sadd(f"{session_key}:products_viewed", interaction.product_id)
            elif interaction.interaction_type == UserInteractionType.SEARCH:
                search_query = interaction.context.get('search_query', '')
                if search_query:
                    self.redis_client.sadd(f"{session_key}:search_queries", search_query)

        except Exception as e:
            print(f"Error updating session data: {e}")

    def _check_profile_update_trigger(self, user_id: str):
        """Check if profile should be updated based on interaction count"""
        try:
            interaction_count_key = f"profile_update_counter:{user_id}"
            count = self.redis_client.incr(interaction_count_key)

            if count >= self.profile_update_threshold:
                self._trigger_profile_update(user_id)
                self.redis_client.delete(interaction_count_key)

        except Exception as e:
            print(f"Error checking profile update trigger: {e}")

    def _trigger_profile_update(self, user_id: str):
        """Trigger user profile update based on recent interactions"""
        try:
            # This would integrate with the personalization engine
            # For now, we'll just log the trigger
            print(f"Profile update triggered for user {user_id}")

        except Exception as e:
            print(f"Error triggering profile update: {e}")

    def _process_real_time_interaction(self, interaction: UserInteraction):
        """Process interaction for real-time insights"""
        try:
            # Update real-time counters
            user_key = f"user_stats:{interaction.user_id}"
            self.redis_client.hincrby(user_key, 'total_interactions', 1)
            self.redis_client.hincrby(user_key, f"interactions_{interaction.interaction_type.value}", 1)

            # Update product popularity
            product_key = f"product_stats:{interaction.product_id}"
            self.redis_client.hincrby(product_key, 'total_interactions', 1)
            self.redis_client.hincrby(product_key, f"interactions_{interaction.interaction_type.value}", 1)

        except Exception as e:
            print(f"Error processing real-time interaction: {e}")

    def _calculate_avg_session_length(self, user_id: str) -> float:
        """Calculate average session length for user"""
        try:
            # This would calculate from session data
            # For now, return a placeholder
            return 15.5  # minutes

        except Exception as e:
            print(f"Error calculating session length: {e}")
            return 0.0

    def _calculate_engagement_score(self, interactions: List[UserInteraction]) -> float:
        """Calculate user engagement score based on interactions"""
        try:
            if not interactions:
                return 0.0

            total_score = 0.0

            for interaction in interactions:
                weight = self.interaction_weights.get(interaction.interaction_type, 1.0)

                # Apply time decay (more recent interactions have higher weight)
                days_ago = (datetime.utcnow() - interaction.timestamp).days
                time_decay = max(0.1, 1.0 - (days_ago * 0.1))

                total_score += weight * time_decay

            # Normalize by number of interactions
            return min(100.0, total_score / len(interactions) * 10)

        except Exception as e:
            print(f"Error calculating engagement score: {e}")
            return 0.0

# Export main class
__all__ = ['BehaviorTracker']
