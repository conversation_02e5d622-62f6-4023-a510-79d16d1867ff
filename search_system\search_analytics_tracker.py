"""
Search Analytics Tracker
Comprehensive search analytics tracking, performance monitoring, and optimization system
"""

import time
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from collections import defaultdict
from enum import Enum
import redis
from sqlalchemy import create_engine, Column, String, Integer, Float, DateTime, Text, Boolean, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from config import DATABASE_URL, REDIS_URL

class SearchEvent(Enum):
    """Search event types for analytics tracking"""
    SEARCH = "search"
    AUTOCOMPLETE = "autocomplete"
    SUGGESTION_CLICK = "suggestion_click"
    RESULT_CLICK = "result_click"
    FILTER_APPLIED = "filter_applied"
    SORT_CHANGED = "sort_changed"
    PAGE_CHANGED = "page_changed"

Base = declarative_base()

@dataclass
class SearchEventData:
    """Search event data structure"""
    event_id: str
    user_id: Optional[str]
    session_id: str
    query: str
    filters: Dict[str, Any]
    search_type: str  # 'simple', 'advanced', 'complex'
    timestamp: datetime
    results_count: int
    response_time_ms: float
    elasticsearch_time_ms: float
    clicked_results: List[str] = None
    conversion_events: List[str] = None
    user_agent: str = None
    ip_address: str = None

@dataclass
class SearchPerformanceMetrics:
    """Search performance metrics"""
    total_searches: int
    avg_response_time: float
    avg_elasticsearch_time: float
    avg_results_count: float
    success_rate: float
    click_through_rate: float
    conversion_rate: float
    popular_queries: List[Dict[str, Any]]
    popular_filters: List[Dict[str, Any]]
    performance_trends: Dict[str, List[float]]

class SearchAnalyticsModel(Base):
    """Database model for search analytics - compatible with app.py"""
    __tablename__ = 'search_analytics'

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, nullable=True)  # Foreign key to users.id
    guest_session_id = Column(String(100), nullable=True)
    search_query = Column(String(500), nullable=False)  # Renamed from 'query'
    search_type = Column(String(50), nullable=False, default='text')
    results_count = Column(Integer, nullable=False, default=0)
    filters_applied = Column(JSON, nullable=True)  # Renamed from 'filters'
    clicked_results = Column(JSON, nullable=True)
    session_id = Column(String(100), nullable=True)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(String(500), nullable=True)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)

    # Additional fields for advanced analytics (optional)
    response_time_ms = Column(Float, nullable=True)
    elasticsearch_time_ms = Column(Float, nullable=True)
    conversion_events = Column(JSON, nullable=True)

class SearchClickModel(Base):
    """Database model for search result clicks"""
    __tablename__ = 'search_clicks'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    search_analytics_id = Column(Integer, nullable=False)  # References search_analytics.id
    product_id = Column(Integer, nullable=False)  # References products.id
    position = Column(Integer, nullable=False)
    timestamp = Column(DateTime, nullable=False, default=datetime.utcnow)
    user_id = Column(Integer, nullable=True)  # References users.id
    session_id = Column(String(64), nullable=False)

class SearchConversionModel(Base):
    """Database model for search conversions"""
    __tablename__ = 'search_conversions'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    search_analytics_id = Column(Integer, nullable=False)  # References search_analytics.id
    product_id = Column(Integer, nullable=False)  # References products.id
    conversion_type = Column(String(20), nullable=False)  # 'add_to_cart', 'purchase', 'wishlist'
    conversion_value = Column(Float, nullable=True)
    timestamp = Column(DateTime, nullable=False, default=datetime.utcnow)
    user_id = Column(Integer, nullable=True)  # References users.id
    session_id = Column(String(64), nullable=False)

class SearchAnalyticsTracker:
    """Main search analytics tracking system"""
    
    def __init__(self):
        self.engine = create_engine(DATABASE_URL)
        Base.metadata.create_all(self.engine)
        Session = sessionmaker(bind=self.engine)
        self.session = Session()
        
        # Redis for real-time analytics
        self.redis_client = redis.from_url(REDIS_URL) if REDIS_URL else None
        
        # Performance tracking
        self.performance_cache = {}
        self.cache_ttl = 300  # 5 minutes
    
    def track_search(self, search_event: SearchEventData) -> str:
        """Track a search event"""
        try:
            # Store in database
            analytics_record = SearchAnalyticsModel(
                user_id=int(search_event.user_id) if search_event.user_id else None,
                guest_session_id=search_event.session_id if not search_event.user_id else None,
                session_id=search_event.session_id,
                search_query=search_event.query,
                filters_applied=search_event.filters,
                search_type=search_event.search_type,
                created_at=search_event.timestamp,
                results_count=search_event.results_count,
                response_time_ms=search_event.response_time_ms,
                elasticsearch_time_ms=search_event.elasticsearch_time_ms,
                clicked_results=search_event.clicked_results or [],
                conversion_events=search_event.conversion_events or [],
                user_agent=search_event.user_agent,
                ip_address=search_event.ip_address
            )
            
            self.session.add(analytics_record)
            self.session.commit()

            # Update real-time metrics in Redis
            if self.redis_client:
                self._update_realtime_metrics(search_event)

            return analytics_record.id  # Return the database ID instead of event_id
            
        except Exception as e:
            self.session.rollback()
            print(f"Error tracking search: {e}")
            return None
    
    def track_click(self, search_analytics_id: int, product_id: int, position: int,
                   user_id: Optional[int] = None, session_id: str = None) -> str:
        """Track a search result click"""
        try:
            click_record = SearchClickModel(
                search_analytics_id=search_analytics_id,
                product_id=product_id,
                position=position,
                user_id=user_id,
                session_id=session_id or str(uuid.uuid4())
            )
            
            self.session.add(click_record)
            self.session.commit()
            
            # Update click tracking in Redis
            if self.redis_client:
                self.redis_client.hincrby(f"search_clicks:{search_analytics_id}", product_id, 1)
                self.redis_client.expire(f"search_clicks:{search_analytics_id}", 86400)  # 24 hours
            
            return click_record.id
            
        except Exception as e:
            self.session.rollback()
            print(f"Error tracking click: {e}")
            return None
    
    def track_conversion(self, search_analytics_id: int, product_id: int, conversion_type: str,
                        conversion_value: Optional[float] = None, user_id: Optional[int] = None,
                        session_id: str = None) -> str:
        """Track a search conversion"""
        try:
            conversion_record = SearchConversionModel(
                search_analytics_id=search_analytics_id,
                product_id=product_id,
                conversion_type=conversion_type,
                conversion_value=conversion_value,
                user_id=user_id,
                session_id=session_id or str(uuid.uuid4())
            )
            
            self.session.add(conversion_record)
            self.session.commit()
            
            # Update conversion tracking in Redis
            if self.redis_client:
                self.redis_client.hincrby(f"search_conversions:{search_analytics_id}", f"{product_id}:{conversion_type}", 1)
                self.redis_client.expire(f"search_conversions:{search_analytics_id}", 86400)  # 24 hours
            
            return conversion_record.id
            
        except Exception as e:
            self.session.rollback()
            print(f"Error tracking conversion: {e}")
            return None
    
    def get_search_performance(self, time_range: str = '24h') -> SearchPerformanceMetrics:
        """Get search performance metrics"""
        cache_key = f"performance_{time_range}"
        
        # Check cache first
        if cache_key in self.performance_cache:
            cached_data, timestamp = self.performance_cache[cache_key]
            if time.time() - timestamp < self.cache_ttl:
                return cached_data
        
        try:
            # Calculate time range
            if time_range == '1h':
                start_time = datetime.utcnow() - timedelta(hours=1)
            elif time_range == '24h':
                start_time = datetime.utcnow() - timedelta(days=1)
            elif time_range == '7d':
                start_time = datetime.utcnow() - timedelta(days=7)
            elif time_range == '30d':
                start_time = datetime.utcnow() - timedelta(days=30)
            else:
                start_time = datetime.utcnow() - timedelta(days=1)
            
            # Query search analytics
            searches = self.session.query(SearchAnalyticsModel).filter(
                SearchAnalyticsModel.timestamp >= start_time
            ).all()
            
            if not searches:
                return SearchPerformanceMetrics(
                    total_searches=0,
                    avg_response_time=0,
                    avg_elasticsearch_time=0,
                    avg_results_count=0,
                    success_rate=0,
                    click_through_rate=0,
                    conversion_rate=0,
                    popular_queries=[],
                    popular_filters=[],
                    performance_trends={}
                )
            
            # Calculate metrics
            total_searches = len(searches)
            avg_response_time = sum(s.response_time_ms for s in searches) / total_searches
            avg_elasticsearch_time = sum(s.elasticsearch_time_ms for s in searches) / total_searches
            avg_results_count = sum(s.results_count for s in searches) / total_searches
            success_rate = len([s for s in searches if s.results_count > 0]) / total_searches
            
            # Calculate click-through rate
            search_ids = [s.id for s in searches]
            clicks = self.session.query(SearchClickModel).filter(
                SearchClickModel.search_id.in_(search_ids)
            ).all()
            click_through_rate = len(set(c.search_id for c in clicks)) / total_searches if total_searches > 0 else 0
            
            # Calculate conversion rate
            conversions = self.session.query(SearchConversionModel).filter(
                SearchConversionModel.search_id.in_(search_ids)
            ).all()
            conversion_rate = len(set(c.search_id for c in conversions)) / total_searches if total_searches > 0 else 0
            
            # Popular queries
            query_counts = defaultdict(int)
            for search in searches:
                if search.query.strip():
                    query_counts[search.query.lower()] += 1
            
            popular_queries = [
                {'query': query, 'count': count}
                for query, count in sorted(query_counts.items(), key=lambda x: x[1], reverse=True)[:10]
            ]
            
            # Popular filters
            filter_counts = defaultdict(int)
            for search in searches:
                if search.filters:
                    for key, value in search.filters.items():
                        if key not in ['page', 'per_page', 'sort_by', 'sort_order']:
                            filter_counts[f"{key}:{value}"] += 1
            
            popular_filters = [
                {'filter': filter_name, 'count': count}
                for filter_name, count in sorted(filter_counts.items(), key=lambda x: x[1], reverse=True)[:10]
            ]
            
            # Performance trends (hourly for last 24h)
            performance_trends = self._calculate_performance_trends(searches, time_range)
            
            metrics = SearchPerformanceMetrics(
                total_searches=total_searches,
                avg_response_time=avg_response_time,
                avg_elasticsearch_time=avg_elasticsearch_time,
                avg_results_count=avg_results_count,
                success_rate=success_rate,
                click_through_rate=click_through_rate,
                conversion_rate=conversion_rate,
                popular_queries=popular_queries,
                popular_filters=popular_filters,
                performance_trends=performance_trends
            )
            
            # Cache the results
            self.performance_cache[cache_key] = (metrics, time.time())
            
            return metrics
            
        except Exception as e:
            print(f"Error getting search performance: {e}")
            return SearchPerformanceMetrics(
                total_searches=0,
                avg_response_time=0,
                avg_elasticsearch_time=0,
                avg_results_count=0,
                success_rate=0,
                click_through_rate=0,
                conversion_rate=0,
                popular_queries=[],
                popular_filters=[],
                performance_trends={}
            )
    
    def _update_realtime_metrics(self, search_event: SearchEventData):
        """Update real-time metrics in Redis"""
        try:
            current_hour = datetime.utcnow().strftime('%Y-%m-%d-%H')
            
            # Update hourly counters
            self.redis_client.hincrby(f"search_metrics:{current_hour}", 'total_searches', 1)
            self.redis_client.hincrby(f"search_metrics:{current_hour}", 'total_response_time', int(search_event.response_time_ms))
            self.redis_client.hincrby(f"search_metrics:{current_hour}", 'total_elasticsearch_time', int(search_event.elasticsearch_time_ms))
            self.redis_client.hincrby(f"search_metrics:{current_hour}", 'total_results', search_event.results_count)
            
            if search_event.results_count > 0:
                self.redis_client.hincrby(f"search_metrics:{current_hour}", 'successful_searches', 1)
            
            # Set expiration for 7 days
            self.redis_client.expire(f"search_metrics:{current_hour}", 604800)
            
            # Update popular queries
            if search_event.query.strip():
                self.redis_client.zincrby('popular_queries', 1, search_event.query.lower())
                self.redis_client.expire('popular_queries', 86400)  # 24 hours
            
        except Exception as e:
            print(f"Error updating real-time metrics: {e}")
    
    def _calculate_performance_trends(self, searches: List[SearchAnalyticsModel], time_range: str) -> Dict[str, List[float]]:
        """Calculate performance trends over time"""
        trends = {
            'response_times': [],
            'elasticsearch_times': [],
            'results_counts': [],
            'success_rates': []
        }
        
        try:
            # Group searches by hour
            hourly_data = defaultdict(list)
            for search in searches:
                hour_key = search.timestamp.strftime('%Y-%m-%d-%H')
                hourly_data[hour_key].append(search)
            
            # Calculate hourly metrics
            for hour_key in sorted(hourly_data.keys()):
                hour_searches = hourly_data[hour_key]
                
                avg_response = sum(s.response_time_ms for s in hour_searches) / len(hour_searches)
                avg_elasticsearch = sum(s.elasticsearch_time_ms for s in hour_searches) / len(hour_searches)
                avg_results = sum(s.results_count for s in hour_searches) / len(hour_searches)
                success_rate = len([s for s in hour_searches if s.results_count > 0]) / len(hour_searches)
                
                trends['response_times'].append(avg_response)
                trends['elasticsearch_times'].append(avg_elasticsearch)
                trends['results_counts'].append(avg_results)
                trends['success_rates'].append(success_rate)
            
        except Exception as e:
            print(f"Error calculating performance trends: {e}")
        
        return trends
    
    def get_query_suggestions(self, partial_query: str, limit: int = 10) -> List[str]:
        """Get query suggestions based on popular searches"""
        try:
            if self.redis_client:
                # Get from Redis popular queries
                popular = self.redis_client.zrevrange('popular_queries', 0, 100, withscores=True)
                suggestions = []
                
                for query_bytes, score in popular:
                    query = query_bytes.decode('utf-8')
                    if partial_query.lower() in query.lower():
                        suggestions.append(query)
                        if len(suggestions) >= limit:
                            break
                
                return suggestions
            else:
                # Fallback to database query
                searches = self.session.query(SearchAnalyticsModel.query).filter(
                    SearchAnalyticsModel.query.ilike(f'%{partial_query}%')
                ).limit(limit * 2).all()
                
                query_counts = defaultdict(int)
                for search in searches:
                    query_counts[search.query.lower()] += 1
                
                return [query for query, count in sorted(query_counts.items(), key=lambda x: x[1], reverse=True)[:limit]]
                
        except Exception as e:
            print(f"Error getting query suggestions: {e}")
            return []
    
    def optimize_search_performance(self) -> Dict[str, Any]:
        """Analyze search performance and provide optimization recommendations"""
        try:
            metrics = self.get_search_performance('7d')
            recommendations = []
            
            # Performance recommendations
            if metrics.avg_response_time > 1000:
                recommendations.append({
                    'type': 'performance',
                    'priority': 'high',
                    'message': 'Average response time is over 1 second. Consider optimizing Elasticsearch queries or adding caching.',
                    'metric': 'response_time',
                    'current_value': metrics.avg_response_time,
                    'target_value': 500
                })
            
            if metrics.success_rate < 0.8:
                recommendations.append({
                    'type': 'relevance',
                    'priority': 'high',
                    'message': 'Search success rate is low. Review search algorithms and consider improving query matching.',
                    'metric': 'success_rate',
                    'current_value': metrics.success_rate,
                    'target_value': 0.9
                })
            
            if metrics.click_through_rate < 0.3:
                recommendations.append({
                    'type': 'relevance',
                    'priority': 'medium',
                    'message': 'Click-through rate is low. Consider improving search result ranking and presentation.',
                    'metric': 'click_through_rate',
                    'current_value': metrics.click_through_rate,
                    'target_value': 0.5
                })
            
            if metrics.conversion_rate < 0.1:
                recommendations.append({
                    'type': 'business',
                    'priority': 'medium',
                    'message': 'Conversion rate is low. Review product recommendations and search result quality.',
                    'metric': 'conversion_rate',
                    'current_value': metrics.conversion_rate,
                    'target_value': 0.2
                })
            
            return {
                'recommendations': recommendations,
                'metrics': asdict(metrics),
                'optimization_score': self._calculate_optimization_score(metrics)
            }
            
        except Exception as e:
            print(f"Error optimizing search performance: {e}")
            return {'recommendations': [], 'metrics': {}, 'optimization_score': 0}
    
    def _calculate_optimization_score(self, metrics: SearchPerformanceMetrics) -> float:
        """Calculate overall search optimization score (0-100)"""
        score = 0
        
        # Response time score (0-25 points)
        if metrics.avg_response_time <= 200:
            score += 25
        elif metrics.avg_response_time <= 500:
            score += 20
        elif metrics.avg_response_time <= 1000:
            score += 15
        elif metrics.avg_response_time <= 2000:
            score += 10
        else:
            score += 5
        
        # Success rate score (0-25 points)
        score += min(25, metrics.success_rate * 25)
        
        # Click-through rate score (0-25 points)
        score += min(25, metrics.click_through_rate * 50)
        
        # Conversion rate score (0-25 points)
        score += min(25, metrics.conversion_rate * 125)
        
        return min(100, score)

# Global tracker instance
search_tracker = SearchAnalyticsTracker()
